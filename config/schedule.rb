# Use this file to easily define all of your cron jobs.
# Learn more: http://github.com/javan/whenever

# Schema cache health monitoring
every 15.minutes do
  runner "SchemaCacheHealthCheckJob.perform_later"
end

# Daily comprehensive schema validation
every 1.day, at: '2:00 am' do
  rake "db:validate_schema_cache"
end

# Weekly proactive schema cache refresh
every 1.week, at: '3:00 am' do
  rake "db:fix_schema_cache"
end
