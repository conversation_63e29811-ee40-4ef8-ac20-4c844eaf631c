Rails.application.routes.draw do
  root 'home#index'
  get '/local/:region', to: 'home#local_page', as: :local_page

  # Test endpoints for schema cache corruption (development/staging only)
  if Rails.env.development? || Rails.env.staging?
    scope :test do
      get '/', to: 'test#schema_cache_test'
      get '/corrupt_and_access', to: 'test#corrupt_and_access'
      get '/access_model', to: 'test#access_model'
      post '/corrupt', to: 'test#corrupt'
      post '/recover', to: 'test#recover'
    end
  end
  namespace :cronjobs do
    get 'import_data_by_model_name/:group_name/:model_name', to: 'imports#import_data_by_model_name', as: :import_data_by_model_name
    get 'raw_import_every_20m',    to: 'imports#raw_import_every_20m',    as: :raw_import_every_20m
    get 'raw_import_every_1h',     to: 'imports#raw_import_every_1h',     as: :raw_import_every_1h
    get 'raw_import_every_5h_10',  to: 'imports#raw_import_every_5h_10',  as: :raw_import_every_5h_10
    get 'raw_import_every_6h',     to: 'imports#raw_import_every_6h',     as: :raw_import_every_6h
    get 'raw_import_every_7h_10',  to: 'imports#raw_import_every_7h_10',  as: :raw_import_every_7h_10
    get 'raw_import_every_9h_50',  to: 'imports#raw_import_every_9h_50',  as: :raw_import_every_9h_50
    get 'raw_import_every_11h_10', to: 'imports#raw_import_every_11h_10', as: :raw_import_every_11h_10
    get 'raw_import_every_12h',    to: 'imports#raw_import_every_12h',    as: :raw_import_every_12h
    get 'raw_import_every_12h_10', to: 'imports#raw_import_every_12h_10', as: :raw_import_every_12h_10
    get 'raw_import_every_0h_10_and_12h_10', to: 'imports#raw_import_every_0h_10_and_12h_10', as: :raw_import_every_0h_10_and_12h_10
  end

  constraints(make: /[^\/]+/, model: /[^\/]+/) do
    controller :search do
      get '/used_car/:make/:model/', to: 'search#index', as: :search
    end

    controller :cars do
      get 'used_car/:make/:model/:car_id', to: 'cars#show', as: :car_detail
      get 'used_car/:make/:model/:car_id/show_more', to: 'cars#show_more', as: :car_show_more
    end

    scope :ajax_crawlable do
      controller :search do
        get '/same_model/:make/:model', to: 'search#same_model', as: :same_model
        get '/popular_models/:make/:model', to: 'search#popular_models', as: :popular_models
      end

      controller :car do
        get '/fetch_seller_info', to: 'cars#seller_info', as: :car_seller_info
        get '/user_car/:make/:model/:car_id/catalog', to: 'cars#catalog', as: :car_catalog
      end
    end

    scope :ajax_v2 do
      controller :search do
        get '/fetch_partial_user_review/:make/:model', to: 'search#fetch_partial_user_review', as: :fetch_partial_user_review
        get '/used_car/:make/:model/description', to: 'search#description', as: :description
      end

      controller :cars do
        get '/fetch_inquiries_counter', to: 'cars#fetch_inquiries_counter', as: :car_inquiries_counter
        get '/fetch_modals', to: 'cars#fetch_modals', as: :car_fetch_modals
        get '/fetch_recommend_cars', to: 'cars#fetch_recommend_cars', as: :car_fetch_recommend_cars
      end

      controller :coupon_tickets do
        post '/precoupon_register', to: 'coupon_tickets#create'
      end
    end
  end

  controller :search do
    get '/advancedsearch', to: 'search#advanced_search', as: :advanced_search
    post '/advancedsearch', to: 'search#advanced_search', as: :exec_advanced_search
    get 'used_car', to: 'search#search_redirect', as: :redirect
  end

  controller :sessions do
    get '/simple/loginregist/', to: 'sessions#simple_loginregist', as: :simple_loginregist
  end

  scope :ajax_v2 do
    resources :currencies, only: :create, constraints: { format: 'json' }
    resources :promotions, only: [] do
      collection do
        get :check_affiliate
      end
    end

    controller :favourites do
      match '/register_favorite/:car_id', to: 'cars/favourites#register_favorite', via: [:post, :delete]
    end

    controller :search do
      post '/advancedsearch/search_counter', to: 'search#search_counter'
      post 'change_sort_condition', to: 'search#change_sort_condition'
      post 'update_per_page', to: 'search#update_per_page'
      get '/favorite_cars', to: 'search#favorite_cars'
      get '/inquiries_cars', to: 'search#inquiries_cars'
      get '/search_fetch_modals', to: 'search#fetch_modals'
      get 'export_performance_rate(/:make/:model)', to: 'search#fetch_export_performance_rate', as: :export_performance_rate_chart
      get 'stock_count(/:make/:model)', to: 'search#fetch_stock_count', as: :stock_count_chart
      get 'condition_export_performance_rate/:param_name/:param_value/', to: 'search#fetch_export_performance_rate_query', as: :export_performance_rate_query_chart
      get 'condition_stock_count/:param_name/:param_value/', to: 'search#fetch_stock_count_query', as: :stock_count_query_chart
    end

    controller :home do
      get '/new_arrival_cars', to: 'home#new_arrival_cars'
      get '/browsing_history', to: 'home#browsing_history'
      get '/fetch_car_ranking', to: 'home#fetch_car_ranking'
      get '/fetch_popular_ranking', to: 'home#fetch_popular_ranking'
      get '/fetch_local_infomation', to: 'home#fetch_local_infomation'
      get '/fetch_testimonials', to: 'home#fetch_testimonials'
      get '/fetch_car_matching_by_country', to: 'home#fetch_car_matching_by_country'
    end

    resources :estimated_total_prices, only: [], constraints: { format: 'json' }, module: 'estimated_total_prices' do
      collection do
        resources :nearest_ports, only: :index, as: 'estimated_total_prices_ports'
        resources :options, only: :index, as: 'estimated_total_prices_options'
        resources :alerts, only: :index, as: 'estimated_total_prices_alerts'
        resources :calculates, only: :index, as: 'estimated_total_prices_calculates'
        resource :cookies, only: :update, as: 'estimated_total_prices_cookies'
      end
    end

    scope :messages do
      get '/notify_counter', to: 'messages#notify_counter'
    end

    resources :transactions, only: [], constraints: { format: 'json' }, module: 'transactions' do
      collection do
        resource :offers, only: :create do
          post '/temp', to: 'offers#create_temp'
          get '/temp', to: 'offers#show_temp'
          post '/create_offer_with_ab_test_simple_signup', to: 'offers#create_offer_with_ab_test_simple_signup'
        end
      end
    end

    controller :users do
      post '/users/signin', to: 'users#signin', constraints: { format: 'json' }
      post '/users/signup', to: 'users#signup', constraints: { format: 'json' }
      post '/users/shortcutmember', to: 'users#shortcutmember', constraints: { format: 'json' }
      get '/users/shortcutmember', to: 'users#shortcutmember', constraints: { format: 'json' }
      post 'users/user_signed_in', to: 'users#user_signed_in'
    end

    controller :search_forms do
      get 'init_search_form_data', to: 'master_data_loader/search_forms#index'
    end

    controller :backorders do
      get 'backorders/data', to: 'backorders#fetch_form_data'
      post 'backorders/create', to: 'backorders#create'
      get 'backorders/lowest_price', to: 'backorders#fetch_lowest_price', as: :backorder_lowest_price
    end
  end

  get 'offer/thankyou', to: 'offers#thank_you', as: :offers_thank_you

  controller :error_page do
    get 'errorpage', to: 'error_page#index', as: :errorpage
  end

  controller :makes do
    get 'allmakeslist', to: 'makes#index', as: :all_makes
  end


  constraints(maker_name: /[^\/]+/) do
    controller :make_models do
      get 'makemodellist/:maker_name', to: 'make_models#index', as: :make_model_list
      get '/used_car/:maker_name/', to: 'make_models#index', as: :model_list
    end

    scope :ajax_v2 do
      controller :make_models do
        get 'makemodellist/:maker_name/fetch_popular_rankings', to: 'make_models#fetch_popular_rankings', as: :make_model_rankings
      end
    end
  end

  controller :backorders do
    get 'backorder', to: 'backorders#new', as: :backorder
    get 'backorder2', to: 'backorders#new', as: :backorder2
    get 'backorder/thanks', to: 'backorders#thanks'
    get 'backorder2/thanks', to: 'backorders#thanks'
  end

  controller :landing_pages do
    get 'listing/l24', to: 'landing_pages#kenya', as: :kenya_landing_page
    get 'listing/l25', to: 'landing_pages#zambia', as: :zambia_landing_page
    get 'listing/l26', to: 'landing_pages#backorder', as: :backorder_landing_page
    get 'campaign/20th_anniversary', to: 'landing_pages#anniversary', as: :anniversary_landing_page
  end

  mount Split::Dashboard, at: 'split'
end
