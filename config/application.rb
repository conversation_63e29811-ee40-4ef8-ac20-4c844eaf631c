require_relative 'boot'
require 'rails/all'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

require_relative 'figaro'

Dir['./app/middlewares/*.rb'].each do |file|
  require file
end

module TCVWebV2
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.0

    if Rails.env.development? || Rails.env.test?
      config.session_store(
        :redis_store,
        servers: ENV.fetch('REDIS_SESSION_URL'),
        expire_after: 15.days,
        key: '_tcv_web_v2_session'
      )

      config.cache_store = :redis_cache_store, {
        driver: :hiredis,
        timeout: 30,
        url: ENV.fetch('REDIS_CACHE_URL')
      }
    else
      if ENV['REDIS_SESSION_URL'].present?
        config.session_store(
          :redis_store,
          servers: [
            {
              host: ENV.fetch('REDIS_HOST'),
              port: ENV.fetch('REDIS_PORT'),
              db: 3,
              password: ENV.fetch('REDIS_AUTH_STRING'),
              ssl: true,
              :ssl_params => {
                :ca_file => "./server-ca.pem"
              },
              driver: :ruby,
            },
          ],
          expire_after: 15.days,
          key: '_tcv_web_v2_session'
        )

        config.cache_store = :redis_cache_store, {
          :host => ENV.fetch('REDIS_HOST'),
          :port => ENV.fetch('REDIS_PORT'),
          :password => ENV.fetch('REDIS_AUTH_STRING'),
          :ssl => :true,
          :db => 2,
          :driver => :ruby,
          :ssl_params => {
            :ca_file => './server-ca.pem'
          }
        }
      end
    end

    # TODO: Configure this for mail delivery
    # config.action_mailer.delivery_method = :smtp
    # config.action_mailer.deliver_later_queue_name = :mailers
    # config.action_mailer.smtp_settings = {
    #   address: 'mailcatcher',
    #   port: 1025
    # }

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")
    config.paths.add 'lib', eager_load: true

    config.middleware.insert_before Rack::ETag, Rack::Deflater
    config.middleware.use CustomLoggingMiddleware
    config.middleware.use HandleErrorMiddleware

    # Add schema cache recovery middleware for production
    if Rails.env.production?
      config.middleware.use SchemaCacheRecoveryMiddleware
    end
  end
end
