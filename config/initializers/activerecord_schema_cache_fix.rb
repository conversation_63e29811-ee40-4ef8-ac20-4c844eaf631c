# Fix for production schema cache issues that cause "undefined method" errors
# for database attributes that exist but ActiveRecord can't access

Rails.application.config.after_initialize do
  # Force reload schema cache for all models if we detect issues
  ActiveRecord::Base.descendants.each do |model|
    next if model.abstract_class?
    
    begin
      # Try to access the columns_hash to detect if schema cache is corrupted
      model.columns_hash
    rescue => e
      Rails.logger.warn "Schema cache issue detected for #{model.name}: #{e.message}"
      model.reset_column_information
    end
  end
end

# Add a method to ApplicationRecord to handle attribute access safely
module ActiveRecordAttributeFix
  def method_missing(method_name, *args, &block)
    # Check if this is an attribute accessor that should exist
    if has_attribute?(method_name)
      # Force reload column information and try again
      self.class.reset_column_information
      if respond_to?(method_name)
        return send(method_name, *args, &block)
      end
    end
    
    super
  end

  def respond_to_missing?(method_name, include_private = false)
    has_attribute?(method_name) || super
  end
end

# Apply the fix to ApplicationRecord
ActiveRecord::Base.prepend(ActiveRecordAttributeFix)
