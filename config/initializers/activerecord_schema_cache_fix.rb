# Fix for production schema cache issues that cause "undefined method" errors
# for database attributes that exist but ActiveRecord can't access

module ActiveRecordSchemaCacheFix
  extend ActiveSupport::Concern

  # Track models that have been fixed to avoid infinite loops
  class_attribute :_schema_cache_fixed, default: false

  module ClassMethods
    def reset_column_information_with_retry
      return if _schema_cache_fixed

      begin
        reset_column_information
        # Test if columns are accessible
        columns_hash
        self._schema_cache_fixed = true
        Rails.logger.info "[SchemaCacheFix] Successfully reset column information for #{name}"
      rescue => e
        Rails.logger.error "[SchemaCacheFix] Failed to reset column information for #{name}: #{e.message}"
        raise e
      end
    end

    def ensure_schema_loaded
      return if _schema_cache_fixed
      return if abstract_class?

      begin
        # Try to access columns to detect corruption
        columns_hash.present?
      rescue => e
        Rails.logger.warn "[SchemaCacheFix] Schema cache corruption detected for #{name}: #{e.message}"
        reset_column_information_with_retry
      end
    end
  end

  # Safe attribute access with automatic schema cache recovery
  def method_missing(method_name, *args, &block)
    method_str = method_name.to_s

    # Check if this looks like an attribute accessor
    if method_str.match?(/\A[a-z_][a-z0-9_]*[?=]?\z/) && has_attribute?(method_str.chomp('?').chomp('='))
      Rails.logger.warn "[SchemaCacheFix] Attribute method missing for #{self.class.name}##{method_name}, attempting recovery"

      # Try to recover by resetting schema cache
      self.class.reset_column_information_with_retry

      # Try the method call again after recovery
      if respond_to?(method_name)
        Rails.logger.info "[SchemaCacheFix] Successfully recovered #{self.class.name}##{method_name}"
        return send(method_name, *args, &block)
      end
    end

    super
  end

  def respond_to_missing?(method_name, include_private = false)
    method_str = method_name.to_s

    # For attribute-like methods, check if we have the attribute
    if method_str.match?(/\A[a-z_][a-z0-9_]*[?=]?\z/)
      attribute_name = method_str.chomp('?').chomp('=')
      return true if has_attribute?(attribute_name)
    end

    super
  end
end

# Initialize schema cache fix on application startup
Rails.application.config.after_initialize do
  next unless Rails.env.production?

  Rails.logger.info "[SchemaCacheFix] Initializing schema cache validation for production environment"

  # Apply the fix to ApplicationRecord
  ActiveRecord::Base.include(ActiveRecordSchemaCacheFix)

  # Validate schema cache for critical models on startup
  critical_models = %w[
    TAggregateRankingPoint
    TAggregateOffer
    TPopularRanking
    MasterMake
    MasterModel
  ]

  critical_models.each do |model_name|
    begin
      model_class = model_name.constantize
      model_class.ensure_schema_loaded
    rescue NameError
      Rails.logger.warn "[SchemaCacheFix] Model #{model_name} not found, skipping validation"
    rescue => e
      Rails.logger.error "[SchemaCacheFix] Failed to validate schema for #{model_name}: #{e.message}"
    end
  end

  Rails.logger.info "[SchemaCacheFix] Schema cache validation completed"
end

# Health check endpoint for schema cache status
if defined?(Rails::HealthCheck)
  Rails::HealthCheck.add_check :schema_cache do
    begin
      # Test a few critical models
      TAggregateRankingPoint.columns_hash.present? &&
      MasterMake.columns_hash.present?
    rescue => e
      Rails.logger.error "[HealthCheck] Schema cache check failed: #{e.message}"
      false
    end
  end
end
