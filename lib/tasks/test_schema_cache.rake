namespace :test do
  desc "Test schema cache corruption and recovery"
  task schema_cache_corruption: :environment do
    unless Rails.env.development? || Rails.env.staging?
      puts "❌ This task can only be run in development or staging environment"
      exit 1
    end

    puts "🧪 Testing schema cache corruption and recovery..."
    
    # Test model
    model = TAggregateRankingPoint
    
    puts "1️⃣ Testing normal operation..."
    begin
      record = model.first
      if record
        puts "   ✅ Normal access: #{record.maker_nm} #{record.model_nm}"
      else
        puts "   ⚠️  No records found, creating test record..."
        record = model.create!(
          maker_id: 999,
          model_id: 999,
          maker_nm: "Test Maker",
          model_nm: "Test Model",
          ranking_point: 100,
          create_date: Time.current,
          update_date: Time.current
        )
        puts "   ✅ Test record created: #{record.maker_nm} #{record.model_nm}"
      end
    rescue => e
      puts "   ❌ Error in normal operation: #{e.message}"
      exit 1
    end
    
    puts "\n2️⃣ Simulating schema cache corruption..."
    SchemaCacheRecoveryMiddleware.simulate_corruption(model)
    
    puts "\n3️⃣ Testing corrupted state..."
    begin
      record = model.first
      record.maker_nm  # This should fail
      puts "   ❌ Corruption simulation failed - attribute still accessible"
    rescue NoMethodError => e
      puts "   ✅ Corruption successful: #{e.message}"
    rescue => e
      puts "   ⚠️  Unexpected error: #{e.message}"
    end
    
    puts "\n4️⃣ Testing manual recovery..."
    begin
      model.reset_column_information
      record = model.first
      puts "   ✅ Recovery successful: #{record.maker_nm} #{record.model_nm}"
    rescue => e
      puts "   ❌ Recovery failed: #{e.message}"
    end
    
    puts "\n🎉 Schema cache corruption test completed!"
    puts "\n💡 To test middleware recovery:"
    puts "   1. Run: SchemaCacheRecoveryMiddleware.simulate_corruption"
    puts "   2. Make a web request that uses TAggregateRankingPoint"
    puts "   3. Check logs for recovery messages"
  end

  desc "Simulate schema cache corruption for web testing"
  task corrupt_schema_cache: :environment do
    unless Rails.env.development? || Rails.env.staging?
      puts "❌ This task can only be run in development or staging environment"
      exit 1
    end

    puts "🧪 Corrupting schema cache for web testing..."
    SchemaCacheRecoveryMiddleware.simulate_corruption(TAggregateRankingPoint)
    puts "✅ Schema cache corrupted!"
    puts "💡 Now make a web request to trigger middleware recovery"
    puts "   Example: Visit a search page that shows popular models"
  end
end
