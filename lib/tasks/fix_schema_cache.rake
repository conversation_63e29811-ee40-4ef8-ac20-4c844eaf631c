namespace :db do
  desc "Fix schema cache issues in production"
  task fix_schema_cache: :environment do
    puts "Fixing schema cache issues..."
    
    # Clear all schema caches
    ActiveRecord::Base.clear_cache!
    
    # Reset column information for all models
    ActiveRecord::Base.descendants.each do |model|
      next if model.abstract_class?
      
      begin
        puts "Resetting column information for #{model.name}"
        model.reset_column_information
        
        # Test if we can access the columns
        model.columns_hash
        puts "✓ #{model.name} schema cache fixed"
      rescue => e
        puts "✗ Error with #{model.name}: #{e.message}"
      end
    end
    
    puts "Schema cache fix completed!"
  end
end
