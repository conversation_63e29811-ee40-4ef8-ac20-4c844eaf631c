namespace :db do
  desc "Fix schema cache issues in production"
  task fix_schema_cache: :environment do
    puts "🔧 Starting schema cache recovery process..."

    # Clear all schema caches
    puts "📝 Clearing ActiveRecord cache..."
    ActiveRecord::Base.clear_cache!

    # Track statistics
    fixed_count = 0
    error_count = 0
    critical_models = %w[TAggregateRankingPoint TAggregateOffer TPopularRanking MasterMake MasterModel]

    # Reset column information for all models
    ActiveRecord::Base.descendants.each do |model|
      next if model.abstract_class?

      begin
        print "🔄 Resetting #{model.name}... "
        model.reset_column_information

        # Test if we can access the columns and perform basic operations
        model.columns_hash
        model.limit(1).to_a if model.table_exists?

        puts "✅"
        fixed_count += 1

        # Extra validation for critical models
        if critical_models.include?(model.name)
          puts "  🎯 Critical model #{model.name} validated successfully"
        end

      rescue => e
        puts "❌ Error: #{e.message}"
        error_count += 1

        # Log detailed error for critical models
        if critical_models.include?(model.name)
          puts "  ⚠️  CRITICAL: #{model.name} failed validation!"
          puts "     Error: #{e.message}"
          puts "     Backtrace: #{e.backtrace.first(3).join(', ')}"
        end
      end
    end

    puts "\n📊 Schema cache recovery completed!"
    puts "   ✅ Successfully fixed: #{fixed_count} models"
    puts "   ❌ Errors encountered: #{error_count} models"

    if error_count > 0
      puts "\n⚠️  Some models still have issues. Consider:"
      puts "   1. Checking database connectivity"
      puts "   2. Verifying migrations are up to date"
      puts "   3. Restarting the application server"
      exit 1
    else
      puts "\n🎉 All models are healthy!"
    end
  end

  desc "Validate schema cache health"
  task validate_schema_cache: :environment do
    puts "🔍 Validating schema cache health..."

    critical_models = [TAggregateRankingPoint, TAggregateOffer, TPopularRanking]
    issues_found = []

    critical_models.each do |model|
      begin
        print "🔍 Testing #{model.name}... "

        # Test column access
        model.columns_hash

        # Test basic query
        model.limit(1).to_a

        # Test attribute access on a sample record
        sample = model.first
        if sample
          sample.attributes.each_key do |attr|
            sample.send(attr) if sample.respond_to?(attr)
          end
        end

        puts "✅"
      rescue => e
        puts "❌"
        issues_found << { model: model.name, error: e.message }
      end
    end

    if issues_found.empty?
      puts "\n🎉 Schema cache is healthy!"
    else
      puts "\n⚠️  Issues found:"
      issues_found.each do |issue|
        puts "   - #{issue[:model]}: #{issue[:error]}"
      end
      puts "\nRun 'rails db:fix_schema_cache' to attempt repairs."
      exit 1
    end
  end
end
