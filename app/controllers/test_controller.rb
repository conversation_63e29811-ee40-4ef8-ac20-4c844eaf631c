class TestController < ApplicationController
  # Skip authentication for testing
  skip_before_action :verify_authenticity_token
  
  # Only allow in development/staging
  before_action :ensure_test_environment
  
  def schema_cache_test
    render json: {
      message: "Schema cache test endpoint",
      environment: Rails.env,
      available_actions: [
        "GET /test/corrupt_and_access - Corrupt cache then access model",
        "GET /test/access_model - Just access model (to test after corruption)",
        "POST /test/corrupt - Just corrupt the cache",
        "POST /test/recover - Manually recover cache"
      ]
    }
  end
  
  def corrupt_and_access
    # Corrupt the cache
    SchemaCacheRecoveryMiddleware.simulate_corruption(TAggregateRankingPoint)
    
    # Try to access the model (should trigger middleware recovery)
    begin
      record = TAggregateRankingPoint.first
      result = {
        status: "success",
        message: "Middleware recovery worked!",
        data: {
          maker_nm: record&.maker_nm,
          model_nm: record&.model_nm
        }
      }
    rescue => e
      result = {
        status: "error", 
        message: "Middleware recovery failed",
        error: e.message
      }
    end
    
    render json: result
  end
  
  def access_model
    begin
      record = TAggregateRankingPoint.first
      render json: {
        status: "success",
        message: "Model access successful",
        data: {
          id: record&.id,
          maker_nm: record&.maker_nm,
          model_nm: record&.model_nm,
          ranking_point: record&.ranking_point
        }
      }
    rescue => e
      render json: {
        status: "error",
        message: "Model access failed", 
        error: e.message,
        backtrace: e.backtrace.first(5)
      }
    end
  end
  
  def corrupt
    SchemaCacheRecoveryMiddleware.simulate_corruption(TAggregateRankingPoint)
    render json: {
      status: "success",
      message: "Schema cache corrupted for TAggregateRankingPoint"
    }
  end
  
  def recover
    TAggregateRankingPoint.reset_column_information
    render json: {
      status: "success", 
      message: "Schema cache manually recovered for TAggregateRankingPoint"
    }
  end
  
  private
  
  def ensure_test_environment
    unless Rails.env.development? || Rails.env.staging?
      render json: { error: "Test endpoints only available in development/staging" }, status: :forbidden
    end
  end
end
