module Metas
  module Search
    include CarHelper

    REJECT_PARAMS_LIST = %w[gfr utm_source utm_medium olddomain fbclid gv so ignfs tp hp gad flt wbraid ct lp ssp
                            darkschemeovr setlang pgcl general_portal_token tm fu mdrv tag _ga gclid
                            utm_campaign utm_content utm_term wipn wirc wfrc _gl fcd isNew mi sb pb uid co eid si
                            fd].freeze
    LIMITED_QUANTITY = 15

    def metas_index
      {
        'twitter:description': metas_description,
        'og:url': metas_canonical,
        'og:title': metas_title,
        'og:type': 'Website',
        'og:description': metas_description,
        title: metas_title,
        description: metas_description,
        robots: metas_robots,
        canonical: metas_canonical
      }
    end

    def metas_advanced_search
      {
        title: title_of_modellist,
        description: description_of_modellist
      }
    end

    def metas_structured_data
      @metas_structured_data ||= unless @hidden_structured_data
                                   valid_params = %w[controller action make model]

                                   if (params.keys - valid_params).empty?
                                     model_valid? ? [make_model_structured_data, breadcrumb_data, item_list] : [default_structured_data]
                                   end
                                 end
    end

    private

    def immutable_title?
      @immutable_title ||= tdk_search_object.condition_size.zero?
    end

    def service_searcher
      @service_searcher ||= ::Search::SearchConditionFilter.new(params, current_country[:country_code])
    end

    def metas_title
      @metas_title ||= if tdk_search_object.condition_size == 2 && tdk_search_object.model?
                         "#{tdk_mapping[:title]} at TCV (formerly tradecarview)"
                       else
                         immutable_title? ? tdk_mapping[:title] : "#{tdk_mapping[:title]} - TCV(former tradecarview)"
                       end
    end

    def metas_description
      tdk_mapping[:description]
    end

    def metas_robots
      return 'noindex' if @status_404
      return 'noindex' if @car_count.to_i < Settings.searcher.min_count

      nil
    end

    def h1_content
      tdk_mapping[:h1] || tdk_mapping[:title]
    end

    def full_text_condition_search
      tdk_search_object.search_condition_text_sp
    end

    def tdk_search_object
      @tdk_search_object ||= ::Tdk::Searcher.new(service_searcher, current_country[:country_code])
    end

    def tdk_mapping
      @tdk ||=
        if generate_combination_meta.present?
          @immutable_title = true
          generate_combination_meta
        elsif tdk_search_object.specially_selected_vehicles?
          tdk_specially_selected_vehicles
        elsif tdk_search_object.vehicle_stock_not_kenya?
          tdk_by_stock
        else
          case tdk_search_object.condition_size
          when 0
            logic_tdk_with_0_search_condition
          when 1
            logic_tdk_with_1_search_condition
          when 2
            logic_tdk_with_2_search_conditions
          when 3
            logic_tdk_with_3_search_conditions
          when 4
            if tdk_search_object.maker?
              tdk_with_specific_couple_condition
            elsif tdk_search_object.bodystyle?
              template_tdk_noindex_v2
            else
              template_tdk_noindex
            end
          when 5
            tdk_search_object.maker? ? template_tdk_noindex_v2 : template_tdk_noindex
          else
            tdk_search_object.maker? || object_has_model_or_bodystyle? ? template_tdk_noindex_v2 : template_tdk_noindex
          end
        end

      @tdk[:title] = tdk_search_object.seller_name if tdk_search_object.seller_id?
      @tdk
    end

    def generate_combination_meta
      @generate_combination_meta ||= ::Tdk::MetaGenerator.new(tdk_search_object, car_count: @car_count, page_num: @page_num).generate_meta_info
    end

    def logic_tdk_with_0_search_condition
      if tdk_search_object.only_rfc? || tdk_search_object.seller_id?
        {
          title: title_with_zero_condition,
          description: description_pattern_2('cars')
        }
      else
        {
          title: title_with_zero_condition,
          description: description_pattern_1('cars', @car_count, 'cars')
        }
      end
    end

    def logic_tdk_with_1_search_condition
      if tdk_search_object.maker?
        title = "#{@tdk_make_name} best price used cars for sale#{@page_num}"
        {
          title: title,
          h1: beauty_h1(title),
          description: description_pattern_3(@tdk_make_name, @car_count, @tdk_make_name)
        }
      elsif tdk_search_object.one_bodystyle?
        if tdk_search_object.body_style_name.present?
          body_style_name = tdk_search_object.body_style_name
          title = "#{body_style_name} best price used cars for sale#{@page_num}"
          {
            title: title,
            h1: beauty_h1(title),
            description: description_pattern_3(body_style_name, @car_count, body_style_name)
          }
        else
          {
            title: "Japanese used cars for sale#{@page_num}",
            description: description_pattern_3(nil, @car_count, nil)
          }
        end
      elsif tdk_search_object.just_a_kodawari?
        kodawari_text = tdk_search_object.kodawari_text
        kodawari_text_with_symbol = "#{kodawari_text}｜" if kodawari_text.present?

        title = "#{kodawari_text_with_symbol}best price Japanese used cars for sale#{@page_num}"
        {
          title: title,
          h1: beauty_h1(title),
          description: description_pattern_2(kodawari_text)
        }
      elsif tdk_search_object.the_kodawari_has_multi_values? && tdk_search_object.bodystyle?
        template_tdk_noindex_v2
      else
        template_tdk_noindex
      end
    end

    def logic_tdk_with_2_search_conditions
      return template_tdk_noindex if !tdk_search_object.maker? && !tdk_search_object.couple_params?

      if tdk_search_object.couple_params?
        kodawari_text = tdk_search_object.kodawari_text
        y_min, y_max  = kodawari_text.split('-')
        des_content   = force_title?(y_min, y_max) ? y_min : kodawari_text
        title = title_for_duplicate_registration_year(y_min, kodawari_text, force_title?(y_min, y_max))
        {
          title: title,
          h1: beauty_h1(title),
          description: description_pattern_2(des_content)
        }
      elsif tdk_search_object.model?
        title = "Used #{interpolate_make_model_text} imports for sale#{@page_num}"
        {
          title: title,
          h1: beauty_h1(title, true),
          description: description_pattern_6(interpolate_make_model_text, @car_count)
        }
      elsif tdk_search_object.just_a_kodawari?
        kodawari_text = tdk_search_object.kodawari_text
        maker_kodawari_text = "#{@tdk_make_name} #{kodawari_text} |"
        title = "#{maker_kodawari_text} best price Japanese used cars for sale#{@page_num}"
        {
          title: title,
          h1: beauty_h1(title),
          description: description_pattern_3("#{@tdk_make_name} #{kodawari_text}", @car_count, @tdk_make_name)
        }
      else
        template_tdk_noindex_v2
      end
    end

    def logic_tdk_with_3_search_conditions
      return template_tdk_noindex if [tdk_search_object.maker?.presence,
                                      tdk_search_object.model?.presence,
                                      tdk_search_object.body_style_name].all?(&:nil?)
      return tdk_with_specific_couple_condition unless tdk_search_object.model?

      if tdk_search_object.kodawari_condition_not_exist?
        {
          title: defautl_title,
          description: description_pattern_3('cars', @car_count, 'cars')
        }
      elsif tdk_search_object.the_kodawari_has_multi_values?
        template_tdk_noindex_v2
      else
        kodawari_text = "｜#{tdk_search_object.kodawari_text}" if tdk_search_object.kodawari_text.present?
        make_model_kodawari_text = "#{interpolate_make_model_text} #{tdk_search_object.kodawari_text}"
        title = "#{make_model_kodawari_text}｜best price Japanese used cars for sale#{@page_num}"
        {
          title: title,
          h1: beauty_h1(title),
          description: description_pattern_3("#{interpolate_make_model_text} #{kodawari_text}", @car_count, interpolate_make_model_text)
        }
      end
    end

    # Not have seaerch condition
    def description_pattern_1(text_1, car_count, text_2)
      "[tradecarview] became【TCV】.
      #{text_1} for sale. #{car_count} Stock#{@page_num} Items. High quality. Fair trade. Secure payment.
      Import #{text_2} directly from Japanese exporters.
      Best quality Japanese used cars for you - TCV."
    end

    # Have kodawari search condition
    def description_pattern_2(value)
      "TCV [former tradecarview] is marketplace that sales used car from Japan.｜#{value} used car stocks#{@page_num} here.
      Large selection of the best priced cars in high quality."
    end

    # Have make and kodawari search consition
    def description_pattern_3(text_1, car_count, text_2)
      "TCV [former tradecarview] is marketplace that sales used car from Japan.｜#{car_count} #{text_1} used car stocks#{@page_num} here.
      Large selection of the best priced #{text_2} cars in high quality."
    end

    def description_pattern_4(country)
      "TCV [former tradecarview] is a marketplace that sells used cars from #{country}." \
        "｜ High-quality used cars from #{country} at the best prices#{@page_num}."
    end

    def description_pattern_5(text)
      "TCV [former tradecarview] is marketplace that sales used car from Japan.｜#{text} used car stocks here. " \
        'Large selection of the best priced cars in high quality.'
    end

    def description_pattern_6(text, car_count)
      "Browse #{car_count} high-quality #{text}#{@page_num} on TCV (formerly tradecarview), the trusted marketplace for Japanese " \
        'used cars. Enjoy secure payments, reliable exporters, and worldwide shipping.'
    end

    def title_with_zero_condition
      "Search by TCV(former tradecarview) | #{defautl_title}"
    end

    def defautl_title
      "Japanese used cars for sale#{@page_num}"
    end

    def title_for_specially_selected_vehicles(text)
      "#{text}｜best price Japanese used cars for sale"
    end

    def h1_for_specially_selected_vehicles(text)
      "#{text}｜Japanese used cars for sale - TCV(former tradecarview)"
    end

    def title_for_duplicate_registration_year(year, kodawari_text, force)
      return "Year #{year}｜best price Japanese used cars for sale#{@page_num}" if force

      "#{sub_text_in_title(with_symbol: false)}#{kodawari_text}｜best price Japanese used cars for sale#{@page_num}"
    end

    def title_for_duplicate_registration_year_v2(text, year, kodawari_text, force)
      return "#{text} & Year #{year}｜best price Japanese used cars for sale#{@page_num}" if force

      "#{text} #{sub_text_in_title}#{kodawari_text}｜best price Japanese used cars for sale#{@page_num}"
    end

    def sub_text_in_title(with_symbol: true)
      return unless tdk_search_object.couple_registration_year?

      with_symbol ? '& Year ' : 'Year '
    end

    def template_tdk_noindex
      {
        title: defautl_title,
        description: description_pattern_2('cars')
      }
    end

    def template_tdk_noindex_v2(with_model: object_has_model_or_bodystyle?)
      {
        title: defautl_title,
        description: description_pattern_3('cars', @car_count, with_model ? 'cars' : nil)
      }
    end

    def force_title?(y_min, y_max)
      tdk_search_object.couple_registration_year? && y_min == y_max
    end

    def interpolate_make_model_text
      "#{@tdk_make_name} #{@tdk_model_name}"
    end

    def tdk_with_specific_couple_condition
      return template_tdk_noindex_v2 if !tdk_search_object.couple_params? || tdk_search_object.condition_hash[:fd].present?

      kodawari_text = tdk_search_object.kodawari_text
      y_min, y_max  = kodawari_text.split('-')
      force_title   = force_title?(y_min, y_max)
      des_content   = force_title ? "#{interpolate_make_model_text} #{y_min}" : "#{interpolate_make_model_text} #{kodawari_text}"
      title = title_for_duplicate_registration_year_v2(interpolate_make_model_text, y_min, kodawari_text, force_title)
      {
        title: title,
        h1: beauty_h1(title),
        description: description_pattern_3(des_content, @car_count, interpolate_make_model_text)
      }
    end

    def beauty_h1(raw_text, new_version = nil)
      if new_version
        raw_text.sub('at TCV (formerly tradecarview)', '')
      else
        "#{raw_text.sub('best price ', '')} - TCV(former tradecarview)"
      end
    end

    def object_has_model_or_bodystyle?
      tdk_search_object.model? || (tdk_search_object.bodystyle? && tdk_search_object.body_style_name.present?)
    end

    def metas_canonical
      @metas_canonical ||=
        unless @status_404 || @status_500
          parsed = URI.parse(request.url)
          query = CGI.parse(parsed.query || '')
          vehicle_stock_use_canonical = MasterInfo::VehiclesInStock.use_in_canonical.pluck(:number).map(&:to_s)
          query.delete_if do |key, value|
            should_delete = REJECT_PARAMS_LIST.include?(key)
            should_keep = key == 'co' && value.intersect?(vehicle_stock_use_canonical) && params[:make] == 'all' && query.keys == ['co']
            should_delete && !should_keep
          end

          custom_parsed = URI.parse(fix_make_model_url)
          custom_parsed.query = query.present? ? URI.encode_www_form(query) : nil

          custom_parsed.to_s.html_safe
        end
    end

    def fix_make_model_url
      return "#{search_url(make: 'all', model: 'all')}/" unless make_valid?

      make_name = @car_make.vc_name_e.downcase
      model_name = model_valid? ? @car_model.vc_name_e.downcase : 'all'
      "#{root_url}used_car/#{escape_param(make_name)}/#{escape_param(model_name)}/"
    end

    def default_structured_data
      {
        '@context': 'https://schema.org/',
        '@type': 'BreadcrumbList',
        itemListElement: {
          '@type': 'ListItem',
          position: 1,
          item: {
            '@id': metas_canonical,
            name: metas_title
          }
        }
      }
    end

    def make_model_structured_data
      {
        '@context': 'https://schema.org/',
        '@id': '#CarProduct',
        '@type': 'Product',
        name: metas_title,
        image: structured_data_image,
        description: metas_description.squish,
        category: 'AggregateOffer',
        brand: [
          {
            '@type': 'Thing',
            name: @car_make.vc_name_e
          },
          {
            '@type': 'Thing',
            name: @car_model.vc_name_e
          },
        ],
        aggregateRating: review_and_rating,
        offers: {
          '@type': 'AggregateOffer',
          itemCondition: 'https://schema.org/UsedCondition',
          ItemAvailability: 'https://schema.org/InStock',
          lowprice: stats_price['min']&.to_i || '',
          highprice: stats_price['max']&.to_i || '',
          priceCurrency: 'USD',
          count: @car_count
        }
      }
    end

    def breadcrumb_data
      {
        '@context': 'https://schema.org',
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            name: 'Home',
            item: 'https://www.tc-v.com/'
          },
          {
            '@type': 'ListItem',
            position: 2,
            name: 'Used Cars',
            item: 'https://www.tc-v.com/used_car/all/all/'
          },
          {
            '@type': 'ListItem',
            position: 3,
            name: @car_make.vc_name_e,
            item: "https://www.tc-v.com/used_car/#{escape_param(params[:make])}/all/"
          },
          {
            '@type': 'ListItem',
            position: 4,
            name: @car_model.vc_name_e,
            item: "https://www.tc-v.com/used_car/#{escape_param(params[:make])}/#{escape_param(params[:model])}/"
          },
        ],
        about: {
          '@id': '#CarProduct'
        }
      }
    end

    def item_list
      {
        '@context': 'https://schema.org',
        '@type': 'ItemList',
        itemListElement: item_list_element,
        about: {
          '@id': '#CarProduct'
        }
      }
    end

    def structured_data_image
      car_is_not_pr = @cars.find { |c| c['StockPR'].nil? }
      return '' unless car_is_not_pr.present?

      car_img_url(car_is_not_pr)
    end

    def stats_price
      @price ||= begin
        query = "MakeID:#{@car_make.id_make} AND ModelID:#{@car_model.id_model}"
        stats_field = 'Price'

        Solr::StatsService.new(query: query, stats_field: stats_field).call.dig('stats', 'stats_fields', 'Price')
      end
    end

    def item_list_element
      @cars.first(LIMITED_QUANTITY).map.with_index do |car, index|
        {
          '@type': 'ListItem',
          position: index + 1,
          url: "https://www.tc-v.com/used_car/#{escape_param(params[:make])}/#{escape_param(params[:model])}/#{car['ItemID']}/"
        }
      end
    end

    def review_and_rating
      user_reviews = Rails.cache.fetch("search_user_review_#{@car_make.id_make}_#{@car_model.id_model}", expires_in: 3.hours) do
        ::Search::UserReviewsService.new(@car_make, @car_model).call
      end
      return unless user_reviews

      review_points = user_reviews[:review].map { |review| review[:review_point].to_f }

      return if review_points.empty?

      {
        '@type': 'AggregateRating',
        reviewCount: review_points.size,
        ratingValue: (review_points.sum / review_points.size).round(1)
      }
    end

    def tdk_by_stock
      country_number = tdk_search_object.condition_hash[:co]
      country_name = case country_number
                     when '410'
                       'Korea'
                     when '826'
                       'the United Kingdom'
                     else
                       MCountry.find_by(number: country_number).country
                     end
      {
        title: "Used Cars for Sale Shipped from #{country_name}#{@page_num}",
        h1: "Used Cars for Sale Shipped from #{country_name}#{@page_num}",
        description: description_pattern_4(country_name)
      }
    end

    def tdk_specially_selected_vehicles
      condition = tdk_search_object.condition_hash
      text = MasterInfo::TdkSpeciallySelectedVehicle.find_by(condition:).text
      {
        title: title_for_specially_selected_vehicles(text),
        h1: h1_for_specially_selected_vehicles(text),
        description: description_pattern_5(text)
      }
    end
  end
end
