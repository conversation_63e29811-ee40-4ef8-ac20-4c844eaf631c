class HealthController < ApplicationController
  skip_before_action :verify_authenticity_token
  skip_before_action :authenticate_user!
  
  def index
    render json: {
      status: 'ok',
      timestamp: Time.current.iso8601,
      checks: {
        database: database_check,
        schema_cache: schema_cache_check,
        redis: redis_check
      }
    }
  end

  def schema_cache
    result = schema_cache_check
    
    if result[:status] == 'ok'
      render json: result
    else
      render json: result, status: :service_unavailable
    end
  end

  private

  def database_check
    begin
      ActiveRecord::Base.connection.execute('SELECT 1')
      { status: 'ok', message: 'Database connection successful' }
    rescue => e
      { status: 'error', message: "Database connection failed: #{e.message}" }
    end
  end

  def schema_cache_check
    begin
      # Test critical models that are prone to schema cache issues
      critical_models = [
        TAggregateRankingPoint,
        TAggregateOffer,
        TPopularRanking
      ]
      
      failed_models = []
      
      critical_models.each do |model|
        begin
          # Test if we can access columns and create a basic query
          model.columns_hash
          model.limit(1).to_a
        rescue => e
          failed_models << { model: model.name, error: e.message }
        end
      end
      
      if failed_models.empty?
        { 
          status: 'ok', 
          message: 'Schema cache is healthy',
          models_checked: critical_models.map(&:name)
        }
      else
        { 
          status: 'error', 
          message: 'Schema cache issues detected',
          failed_models: failed_models
        }
      end
    rescue => e
      { status: 'error', message: "Schema cache check failed: #{e.message}" }
    end
  end

  def redis_check
    begin
      Rails.cache.write('health_check', 'ok', expires_in: 1.minute)
      result = Rails.cache.read('health_check')
      
      if result == 'ok'
        { status: 'ok', message: 'Redis connection successful' }
      else
        { status: 'error', message: 'Redis read/write test failed' }
      end
    rescue => e
      { status: 'error', message: "Redis connection failed: #{e.message}" }
    end
  end
end
