module Tdk
  class Searcher
    attr_reader :condition_hash, :condition_size, :search_service, :country_code

    KODAWARI_FIELDS = %i[
      ac bsty sds eds do dr ecls prcf prct fues nw spp
      mimn mimx fid smo jid emo st tmns uid op
    ].freeze

    ARRAY_FIELDS = %i[tmns fues ecls bsty].freeze
    CHECKBOX_FIELDS = %i[nw spp].freeze

    MAXIMUM_KEY_FOR_KODAWARI = 2
    MAXIMUM_SIZE_OF_FILED_WITH_MULTI_VALUE = 1
    KODAWARI_COUPLE_FIELDS = [%i[prcf prct], %i[sds eds], %i[mimn mimx], %i[fid jid]].freeze
    EXPECTED_KEY_FOR_SP = %i[co st bsty tmns fues dr ecls fd nw ac prcf prct mimn mimx sds eds do].freeze

    def initialize(search_service, country_code)
      @search_service = search_service
      @condition_hash = normalize_params
      @condition_size = @condition_hash.size
      @country_code   = country_code
    end

    def kodawari_condition_not_exist?
      intersection_kodawari_params.empty?
    end

    def maker?
      condition_hash[:make].present?
    end

    def model?
      condition_hash[:model].present?
    end

    def bodystyle?
      body_style_id.present?
    end

    def one_bodystyle?
      temp_body_style_id = body_style_id
      return false if temp_body_style_id.nil?

      temp_body_style_id = temp_body_style_id.split('*')
      temp_body_style_id.instance_of?(Array) && temp_body_style_id.size == MAXIMUM_SIZE_OF_FILED_WITH_MULTI_VALUE
    end

    def just_a_kodawari?
      check_valid_amount_params do |data_parsed|
        data_parsed.instance_of?(Integer) || (data_parsed.is_a?(Array) && data_parsed.size == MAXIMUM_SIZE_OF_FILED_WITH_MULTI_VALUE)
      end
    end

    def the_kodawari_has_multi_values?
      check_valid_amount_params do |data_parsed|
        data_parsed.is_a?(Array) && data_parsed.size > MAXIMUM_SIZE_OF_FILED_WITH_MULTI_VALUE
      end
    end

    def only_rfc?
      @rfc.present?
    end

    def seller_id?
      @uid.present? && @uid.length <= VALID_LENGTH_USER_ID && Search::ParamsService::NUMBER_REGEX === @uid
    end

    def couple_params?
      intersection_kodawari_params.size == MAXIMUM_KEY_FOR_KODAWARI && KODAWARI_COUPLE_FIELDS.include?(intersection_kodawari_params)
    end

    def couple_registration_year?
      KODAWARI_COUPLE_FIELDS.last == intersection_kodawari_params
    end

    def seller_name
      return unless seller_id?

      @seller_name ||= ::Solr::Base.new.find(
        {
          queries: "UserID: #{@uid}",
          field_list: 'DealerName',
          rows: 1
        },
      ).dig('response', 'docs')[0]&.[]('DealerName')
    end

    def kodawari_text
      return if intersection_kodawari_params.size > MAXIMUM_KEY_FOR_KODAWARI

      intersection_kodawari_params.map do |key|
        current_value = data_parser(condition_hash[key], key)
        current_field = MasterInfo::SearchKeyMappingSolr.find_by(key: key)
        beauty_text.call(current_field, current_value, key, false, '-')
      end.join.sub('--', '-')
    end

    def body_style_name
      return if body_style_id.nil?

      bsty_id = body_style_id
      primary_body_style_id, sub_body_style_id = bsty_id.split('.')
      primary_body_style = MPrimaryBodyStyle.find_by(primary_body_style_id: primary_body_style_id)
      return if primary_body_style.nil?

      m_second_body_style = MSecondaryBodyStyle.find_by(primary_body_style_id: primary_body_style_id, secondary_body_style_id: sub_body_style_id)
      sub_body_style = m_second_body_style.present? ? " #{m_second_body_style.name}" : ''

      "#{primary_body_style.name}#{sub_body_style}"
    end

    def search_condition_text_sp
      except_keys       = %i[make model fid smo jid emo spp]
      current_keys      = normalize_params.except(*except_keys).keys
      intersection_keys = EXPECTED_KEY_FOR_SP & current_keys
      return regular_car_text&.lstrip if intersection_keys.empty? && model_year_month_text.nil?

      condition_text = intersection_keys.map do |key|
        next normalize_params[key] if key == :fd
        next new_car_text if key == :nw

        current_value = data_parser(normalize_params[key], key)
        current_field = MasterInfo::SearchKeyMappingSolr.find_by(key: key)
        beauty_text.call(current_field, current_value, key, true, '～', true)
      end.compact.join(', ').gsub('～, ～', ' ～ ')
      return regular_car_text&.lstrip if [model_year_month_text, condition_text].all?(&:blank?)

      target = "'for #{model_year_month_text}#{condition_text}#{regular_car_text}'"
      (intersection_keys.empty? && target.sub(', ', '')) || (target == "'for '" ? '' : target)
    end

    def vehicle_stock_not_kenya?
      MasterInfo::VehiclesInStock.where(show_in_nav: 0, number: condition_hash[:co].to_i).present?
    end

    def specially_selected_vehicles?
      MasterInfo::TdkSpeciallySelectedVehicle.pluck(:condition).include?(condition_hash)
    end

    private

    def number_format(number)
      ActionController::Base.helpers.number_with_delimiter(number)
    end

    def beauty_text
      proc do |current_field, current_value, key_target, normal_engine_text, symbol, smart_phone = false|
        next add_specific_unit(key_target, current_value, normal_engine_text) if current_field.get_current_text
        next current_field.text if current_field.available_text
        next if current_value.is_a?(Array) && current_value.size > MAXIMUM_SIZE_OF_FILED_WITH_MULTI_VALUE

        if Object.const_defined?(current_field.model)
          model = current_field.model
          search_model = convert_to_model(model)
          item_id = current_value.is_a?(Array) ? current_value.join : current_value&.to_i
          value = case model
                  when MArticle.name
                    search_model.find_by(group_name: current_field.group, article_id: item_id)&.en
                  when MPrimaryBodyStyle.name
                    body_style_name
                  when MasterInfo::FobSearchOption.name
                    fetch_option_name(search_model, item_id, smart_phone) || "US$#{number_format(item_id)}"
                  when MasterInfo::MileageSearchOption.name
                    fetch_option_name(search_model, item_id, smart_phone) || "#{number_format(item_id)}km"
                  when MasterInfo::CapacitySearchOption.name
                    fetch_option_name(search_model, item_id, smart_phone) || item_id.to_s
                  when MCountry.name
                    search_model.find_by(number: current_value)&.country
                  when MasterInfo::CarOption.name
                    search_model.find_by(id: item_id)&.name
                  else
                    fetch_option_name(search_model, item_id, smart_phone)
                  end
          next unless value

          add_specific_unit(key_target, value, normal_engine_text, symbol: symbol)
        end
      end
    end

    def model_year_month_text
      return if [normalize_params[:fid], normalize_params[:jid]].all?(&:nil?)

      month_form = "/#{normalize_params[:smo]}" if normalize_params[:smo]
      month_to   = "/#{normalize_params[:emo]}" if normalize_params[:emo]
      "#{normalize_params[:fid]}#{month_form} ～ #{normalize_params[:jid]}#{month_to}, "
    end

    def regular_car_text
      return unless @rfc.presence

      country = ::MCountry.country_has_regular_car(country_code)
      return unless country

      " For #{country.a3}"
    end

    def new_car_text
      normalize_params[:nw].presence ? 'New Stocks' : nil
    end

    def normalize_params
      keys = %w[controller action si]
      search_params = search_service.queries.except(*keys)&.symbolize_keys
      return {} if search_params.nil?

      search_params.each { |key, value| search_params.delete(key) if value == 'all' }
      @uid = search_params.delete(:uid)
      @rfc = search_params.delete(:rfc)
      search_params.delete(:pn)

      search_params.each do |key, value|
        search_params[key] = nil if data_parser(value, key).nil?
      end.compact!

      search_params
    end

    def intersection_kodawari_params
      KODAWARI_FIELDS & condition_hash.keys
    end

    def body_style_id
      condition_hash[:bsty]
    end

    def add_specific_unit(key, value, normal_engine_text, symbol: '-')
      tmp_value = value
      tmp_value = normal_engine_text ? "#{value}cc" : "#{value.split('(').first}cc" if %i[sds eds].include?(key)
      tmp_value = "#{value} Doors" if key == :do
      return "#{tmp_value}#{symbol}" if KODAWARI_COUPLE_FIELDS.map(&:first).include?(key)
      return "#{symbol}#{tmp_value}" if KODAWARI_COUPLE_FIELDS.map(&:last).include?(key)

      tmp_value
    end

    def data_parser(input, type)
      case type
      when *CHECKBOX_FIELDS
        if input.strip.match(Search::ParamsService::NUMBER_REGEX)
          input.to_i == 1 ? 1 : nil
        end
      when *ARRAY_FIELDS
        text = input.to_s
        Search::ParamsService::NUMBER_REGEX === text.gsub(/\*|\./, '') ? text.split('*') : nil
      when *Search::ParamsService::INT_VALUE_LIST.map(&:to_sym)
        # check is number
        input.to_i.positive? && Search::ParamsService::NUMBER_REGEX === input.to_s.strip ? input : nil
      else
        input
      end
    end

    def convert_to_model(service_name)
      raise 'This service is not avaiable!' unless Object.const_defined?(service_name)

      service_name.constantize
    end

    def check_valid_amount_params
      return false if intersection_kodawari_params.size != MAXIMUM_SIZE_OF_FILED_WITH_MULTI_VALUE

      data_parsed = data_parser(*condition_hash[*intersection_kodawari_params], *intersection_kodawari_params)
      final_value = data_parsed.instance_of?(String) ? data_parsed.to_i : data_parsed
      yield(final_value) if block_given?
    end

    def fetch_option_name(model, value, smart_phone)
      option = model.find_by(value: value) || (model.default_value unless smart_phone)

      option&.name
    end
  end
end
