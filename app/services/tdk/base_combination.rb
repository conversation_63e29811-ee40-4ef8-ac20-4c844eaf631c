module Tdk
  class BaseCombination
    attr_reader :searcher, :car_count, :page_num, :condition_hash, :country_code

    SUPPORTED_BODY_STYLE_IDS = [7, 5, 1, 2, 11, 3, 4, 6, 8, 12, 9, 10].freeze
    SUPPORTED_PRICE_RANGES = [
      [:min, 500],
      [500, 1_000],
      [1_000, 1_500],
      [1_500, 2_000],
      [2_000, 2_500],
      [2_500, 5_000],
      [5_000, 10_000],
      [10_000, 20_000],
      [20_000, :max],
    ].freeze
    SUPPORTED_CATEGORIES = [
      { st: '13' },
      { tmns: '6' },
      { fues: '17' },
      { dr: '10' },
      { ac: '2' },
      { ac: '1' },
      { op: '33' },
    ].freeze
    CATEGORY_MAPPING = {
      st: { 13 => 'Left Handle Only' },
      tmns: { 6 => 'Manual' },
      fues: { 17 => 'Diesel' },
      dr: { 10 => '4 wheel drive' },
      ac: { 2 => 'No Accident Only', 1 => 'Accident Not Repaired' },
      op: { 33 => 'Sunroof' }
    }.freeze
    SPACE = ' '.freeze
    EXPECTED_SINGLE_CONDITION_SIZE = 1
    EXPECTED_DUAL_CONDITION_SIZE = 2

    def initialize(searcher, car_count: 0, page_num: '')
      @searcher = searcher
      @car_count = car_count
      @page_num = page_num
      @condition_hash = searcher.condition_hash
      @country_code = searcher.country_code
    end

    def applicable?
      raise NotImplementedError, "#{self.class} must implement #applicable?"
    end

    def generate_meta_info
      raise NotImplementedError, "#{self.class} must implement #generate_meta_info"
    end

    def build_meta_info(text_parts)
      {
        title: "Used #{text_parts.join(SPACE)} imports for sale #{page_num} at TCV (formerly trade carview)",
        description: "Browse #{car_count} high-quality #{text_parts.join(SPACE)} #{page_num} on TCV (formerly trade carview), " \
                     'the trusted marketplace for Japanese used cars. Enjoy secure payments, reliable exporters, and worldwide shipping.',
        h1: "Used #{text_parts.join(SPACE)} imports for sale #{page_num}"
      }
    end

    private

    def body_style_name
      searcher.body_style_name
    end

    def number_format(number)
      ActionController::Base.helpers.number_with_delimiter(number)
    end

    def single_body_style?
      body_style_id = condition_hash[:bsty]
      body_style_id.present? && !body_style_id.include?('*')
    end

    def supported_body_style?
      body_style_id = condition_hash[:bsty]
      return false unless body_style_id.present?
      return false unless single_body_style?

      SUPPORTED_BODY_STYLE_IDS.include?(body_style_id.to_i)
    end

    def supported_price_range?
      price_min = condition_hash[:prcf].presence&.to_i || :min
      price_max = condition_hash[:prct].presence&.to_i || :max
      SUPPORTED_PRICE_RANGES.include?([price_min, price_max])
    end

    def category_text
      CATEGORY_MAPPING.each do |key, values|
        value = condition_hash[key].to_i
        return values[value] if condition_hash[key].present? && values.key?(value)
      end
    end

    def supported_category?
      @supported_category ||= SUPPORTED_CATEGORIES.one? do |category_hash|
        category_hash.all? { |k, v| condition_hash[k] == v }
      end
    end

    def price_range_text
      [condition_hash[:prcf], condition_hash[:prct]]
        .compact
        .map { |price| "US$#{number_format(price)}" }
        .join('-')
    end

    def supported_make?
      condition_hash[:make].present?
    end

    def make_name
      @make_name ||= MasterMake.get_by_vc_name_e(condition_hash[:make]).vc_name_e
    end
  end
end
