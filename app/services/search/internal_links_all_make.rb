module Search
  class InternalLinksAllMake
    NUMBER_OF_SPECIAL_PRICE_CONDITION = 4

    SECTIONS = {
      other_category: { title: 'Other Category', link_info: :other_category_data },
      vehicle_stock: { title: 'Vehicles In Stock', link_info: :vehicle_stock_data },
      body_style: { title: 'BodyStyle', link_info: :body_style_data },
      popular_model: { title: 'Popular Models', link_info: :popular_model_data },
      popular_maker: { title: 'Popular Makes', link_info: :popular_maker_data },
      price: { title: 'Car Price (FOB-US$)', link_info: :price_data },
      special_price: { title: 'Specially Selected Vehicles', link_info: :special_price_data },
      rare_cars: { title: 'Rare Cars List', link_info: :rare_cars_data }
    }.freeze

    def initialize(user_country_code)
      @solr_facet = Solr::FacetService.new
      @condition_data = MasterInfo::InternalLinkAllMakeCondition.all
      @body_style_data = MasterInfo::InternalLink::BodyStyle.all
      @other_category_data = MasterInfo::InternalLink::OtherCategory.all
      @vehicle_stock = MasterInfo::VehiclesInStock.use_in_canonical
      @popular_for_all_make_service = Search::GetPopularForAllMakeService.new
      @car_counter_instance = DataLoader::CarCounterService.new(user_country_code)
    end

    def call
      Rails.cache.fetch("internal_links_all_make_#{@user_country_code}", expires_in: 1.hour) do
        fetch_condition_counts
        SECTIONS.filter_map do |_, section|
          link_info = send(section[:link_info])
          next if link_info.blank?

          { condition: link_info, title: section[:title] }
        end
      end
    end

    private

    def fetch_condition_counts
      response = @solr_facet.call_facet(queries: facet_queries).dig('facet_counts', 'facet_queries')
      @condition_counts = response.map { |_, count| count }
    end

    def facet_queries
      @condition_data.pluck(:query)
    end

    def other_category_data
      @other_category_data.map { |other_category| [[other_category.url, other_category.text, :url], 1] }
    end

    def vehicle_stock_data
      @vehicle_stock.map do |vehicle_stock|
        vehicle_stock_name = vehicle_stock.number == 410 ? 'Korea' : vehicle_stock.name
        [[vehicle_stock.pc_path, vehicle_stock_name, :url], 1]
      end
    end

    def body_style_data
      @body_style_data.map { |body_style| [[body_style.url, body_style.text, :url], 1] }
    end

    def popular_model_data
      models = @popular_for_all_make_service.list_all_model
      return [] if models.blank?

      models.map do |popular_model|
        [
          ["used_car/#{popular_model.maker_nm.downcase}/#{popular_model.model_nm.downcase}/",
           "#{popular_model.maker_nm} #{popular_model.model_nm}", :url], 1
        ]
      end
    end

    def popular_maker_data
      makers = @popular_for_all_make_service.list_all_maker
      return [] if makers.blank?

      makers.map do |popular_maker|
        [
          ["used_car/#{popular_maker.maker_nm.downcase}/all",
           popular_maker.maker_nm, :url], 1
        ]
      end
    end

    def price_data
      @car_counter_instance.process_price_data.filter_map do |price_data|
        next unless price_data[:stock].positive?

        [[price_data[:url], price_data[:range], :url], price_data[:stock]]
      end
    end

    def condition_with_count_data
      @condition_with_count_data ||= @condition_data.map { |condition| [condition.url, condition.text, :url] }.zip(@condition_counts)
    end

    def special_price_data
      condition_with_count_data.first(NUMBER_OF_SPECIAL_PRICE_CONDITION)
    end

    def rare_cars_data
      condition_with_count_data.drop(NUMBER_OF_SPECIAL_PRICE_CONDITION)
    end
  end
end
