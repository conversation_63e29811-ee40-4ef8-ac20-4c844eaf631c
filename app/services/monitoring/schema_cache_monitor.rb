module Monitoring
  class SchemaCacheMonitor
    include Singleton

    CRITICAL_MODELS = %w[
      TAggregateRankingPoint
      TAggregateOffer
      TPopularRanking
      MasterMake
      MasterModel
    ].freeze

    def initialize
      @last_check_time = nil
      @consecutive_failures = 0
      @max_consecutive_failures = 3
    end

    def perform_health_check
      Rails.logger.info "[SchemaCacheMonitor] Starting health check"
      
      results = {
        timestamp: Time.current,
        status: 'healthy',
        models_checked: [],
        failed_models: [],
        warnings: []
      }

      CRITICAL_MODELS.each do |model_name|
        model_result = check_model_health(model_name)
        results[:models_checked] << model_result
        
        if model_result[:status] == 'failed'
          results[:failed_models] << model_result
          results[:status] = 'unhealthy'
        elsif model_result[:status] == 'warning'
          results[:warnings] << model_result
          results[:status] = 'degraded' if results[:status] == 'healthy'
        end
      end

      # Update failure tracking
      if results[:status] == 'unhealthy'
        @consecutive_failures += 1
        handle_consecutive_failures if @consecutive_failures >= @max_consecutive_failures
      else
        @consecutive_failures = 0
      end

      @last_check_time = Time.current
      
      # Send alerts if needed
      send_alerts(results) if should_send_alert?(results)
      
      Rails.logger.info "[SchemaCacheMonitor] Health check completed: #{results[:status]}"
      results
    end

    def check_model_health(model_name)
      start_time = Time.current
      
      begin
        model_class = model_name.constantize
        
        # Test 1: Column access
        columns = model_class.columns_hash
        raise "No columns found" if columns.empty?
        
        # Test 2: Basic query
        model_class.limit(1).to_a
        
        # Test 3: Attribute access on sample record
        sample = model_class.first
        if sample
          # Test a few key attributes
          test_attributes = sample.attributes.keys.first(3)
          test_attributes.each do |attr|
            sample.send(attr) if sample.respond_to?(attr)
          end
        end
        
        duration = ((Time.current - start_time) * 1000).round(2)
        
        {
          model: model_name,
          status: duration > 1000 ? 'warning' : 'healthy',
          duration_ms: duration,
          message: duration > 1000 ? 'Slow response time' : 'OK'
        }
        
      rescue => e
        duration = ((Time.current - start_time) * 1000).round(2)
        
        {
          model: model_name,
          status: 'failed',
          duration_ms: duration,
          error: e.message,
          message: "Schema cache issue detected"
        }
      end
    end

    def should_send_alert?(results)
      return true if results[:status] == 'unhealthy'
      return true if @consecutive_failures >= 2
      
      # Send alert if we haven't checked in a while and there are warnings
      return true if @last_check_time && 
                     (Time.current - @last_check_time) > 1.hour && 
                     results[:warnings].any?
      
      false
    end

    def send_alerts(results)
      Rails.logger.error "[SchemaCacheMonitor] Sending alert for status: #{results[:status]}"
      
      # Send to error reporting service
      if defined?(Sentry)
        Sentry.capture_message(
          "Schema cache health check failed",
          level: results[:status] == 'unhealthy' ? :error : :warning,
          extra: {
            status: results[:status],
            failed_models: results[:failed_models],
            warnings: results[:warnings],
            consecutive_failures: @consecutive_failures
          }
        )
      end
      
      # Log detailed information
      if results[:failed_models].any?
        Rails.logger.error "[SchemaCacheMonitor] Failed models: #{results[:failed_models].map { |m| m[:model] }.join(', ')}"
      end
      
      if results[:warnings].any?
        Rails.logger.warn "[SchemaCacheMonitor] Warning models: #{results[:warnings].map { |m| m[:model] }.join(', ')}"
      end
    end

    def handle_consecutive_failures
      Rails.logger.error "[SchemaCacheMonitor] #{@consecutive_failures} consecutive failures detected, attempting automatic recovery"
      
      begin
        # Attempt automatic recovery
        ActiveRecord::Base.clear_cache!
        
        CRITICAL_MODELS.each do |model_name|
          model_class = model_name.constantize
          model_class.reset_column_information
        end
        
        Rails.logger.info "[SchemaCacheMonitor] Automatic recovery completed"
        
      rescue => e
        Rails.logger.error "[SchemaCacheMonitor] Automatic recovery failed: #{e.message}"
        
        # Send critical alert
        if defined?(Sentry)
          Sentry.capture_exception(e, level: :fatal, extra: {
            context: 'automatic_schema_cache_recovery',
            consecutive_failures: @consecutive_failures
          })
        end
      end
    end

    class << self
      def perform_check
        instance.perform_health_check
      end
    end
  end
end
