- model = escape_param(params[:model])
- content_for :show_negotiation, true
- content_for :serp_thumbnail do
  = render 'shared/pagemap_thumbnail', thumb_url: @car_presenter.car_thumb_url(width: '120', height: '120', type: 'crop')

= render 'shared/modals/check_year_model'
= render 'sp/shared/popups/coupon', text: 'Inquire Now', modalType: '1'
.car-detail-wrapper[
    data-controller='car'
    data-car-item-ids-value="[#{@car_presenter.car[:itemId]}]"
    data-car-seller-id="#{@car_presenter.car[:sellerId]}"
  ]
  = render 'sp/cars/stop_item', account_state: @account_state
  h1.title-block = simple_format @h1_content
  .gallery-block
    = render 'sp/cars/gallery'
    = render 'sp/cars/ask_price', padding: 'pt-40', notify_class: 'inquiry-tooltip'
  .vehicle-info-block
    = render 'sp/cars/vehicle_infomation'
  - if @allow_inquiry
    .order__step-area
      = image_pack_tag 'media/images/panels/order_flo.webp', alt: 'steps for inquiry', loading: 'eager'
  = render 'sp/cars/shared/frame_detail_contact', suffix: 'f1'
  = render 'sp/cars/shared/favorite_button', carId: @car_stock['itemId'], plus_class: ''
  = render 'sp/cars/support_user' if ab_test_support_links_after? && @allow_inquiry
  .specific-info-block
    = render 'sp/cars/specific'
  .options-block
    = render 'sp/cars/options'
    = render 'sp/cars/shared/frame_detail_contact', suffix: 'f2'
    = render 'sp/cars/ask_price', padding: '', notify_class: 'sub-text'
    = render 'sp/cars/shared/favorite_button', carId: @car_stock['itemId'], plus_class: 'custom_button'
    = render 'shared/cars/banner', platform: :sp, size: '300x136'
    = render 'sp/cars/ask_price_bottom'
  .d-none data-car-target='sellerInfoArea' data-car-seller-url=seller_info_path(@car_stock, @allow_inquiry)
  = render 'sp/cars/shared/frame_detail_contact', suffix: 'f3'
  = render 'sp/cars/shared/favorite_button', carId: @car_stock['itemId'], plus_class: ''
  .d-none data-car-target='recommendCars' data-url=recommend_cars_path(@car_stock, @allow_inquiry)
  = render 'sp/cars/shared/favorite_button', carId: @car_stock['itemId'], plus_class: 'custom_button'
  = render 'sp/cars/additional_infomation'
  = render partial: 'sp/cars/internal_links', collection: @internal_links, as: :data
  = render 'sp/shared/main_breadcrumb'
  = render 'sp/shared/btn_scroll_top'
  = render 'sp/cars/shared/modal_detail_contact'
  = render 'shared/countries_require_preship'
  = render 'shared/countries_default_shipping'
= load_popup_assist
