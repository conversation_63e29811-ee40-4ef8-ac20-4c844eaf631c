class SchemaCacheHealthCheckJob < ApplicationJob
  queue_as :default

  def perform
    Rails.logger.info "[SchemaCacheHealthCheckJob] Starting scheduled health check"
    
    begin
      results = Monitoring::SchemaCacheMonitor.perform_check
      
      # Store results in cache for health endpoint
      Rails.cache.write(
        'schema_cache_health_check_results',
        results,
        expires_in: 30.minutes
      )
      
      Rails.logger.info "[SchemaCacheHealthCheckJob] Health check completed with status: #{results[:status]}"
      
    rescue => e
      Rails.logger.error "[SchemaCacheHealthCheckJob] Health check failed: #{e.message}"
      Rails.logger.error "[SchemaCacheHealthCheckJob] Backtrace: #{e.backtrace.first(5).join("\n")}"
      
      # Send error to monitoring
      if defined?(Sentry)
        Sentry.capture_exception(e, level: :error, extra: {
          job: 'SchemaCacheHealthCheckJob',
          context: 'scheduled_health_check'
        })
      end
      
      raise e
    end
  end
end
