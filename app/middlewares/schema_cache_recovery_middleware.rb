class SchemaCacheRecoveryMiddleware
  def initialize(app)
    @app = app
    @recovery_count = 0
    @last_recovery_time = nil
    @max_recoveries_per_hour = 5
  end

  def call(env)
    @app.call(env)
  rescue NoMethodError => e
    # Check if this is a schema cache related error
    if schema_cache_error?(e)
      Rails.logger.error "[SchemaCacheRecovery] Detected schema cache error: #{e.message}"
      Rails.logger.error "[SchemaCacheRecovery] Backtrace: #{e.backtrace.first(10).join("\n")}"
      
      if should_attempt_recovery?
        perform_recovery(e)
        
        # Retry the request once after recovery
        Rails.logger.info "[SchemaCacheRecovery] Retrying request after schema cache recovery"
        return @app.call(env)
      else
        Rails.logger.error "[SchemaCacheRecovery] Recovery limit reached, not attempting recovery"
      end
    end
    
    raise e
  rescue => e
    raise e
  end

  private

  def schema_cache_error?(error)
    error.message.include?("undefined method") &&
    (error.message.include?("_nm") || 
     error.message.include?("_id") ||
     error.backtrace.any? { |line| line.include?("active_record/attribute_methods") })
  end

  def should_attempt_recovery?
    now = Time.current
    
    # Reset counter if it's been more than an hour
    if @last_recovery_time.nil? || (now - @last_recovery_time) > 1.hour
      @recovery_count = 0
    end
    
    @recovery_count < @max_recoveries_per_hour
  end

  def perform_recovery(error)
    @recovery_count += 1
    @last_recovery_time = Time.current
    
    Rails.logger.info "[SchemaCacheRecovery] Performing schema cache recovery (attempt #{@recovery_count}/#{@max_recoveries_per_hour})"
    
    begin
      # Extract model name from error if possible
      model_name = extract_model_name_from_error(error)
      
      if model_name
        Rails.logger.info "[SchemaCacheRecovery] Targeting recovery for model: #{model_name}"
        model_class = model_name.constantize
        model_class.reset_column_information
      else
        Rails.logger.info "[SchemaCacheRecovery] Performing global schema cache reset"
        # Global recovery
        ActiveRecord::Base.clear_cache!
        ActiveRecord::Base.descendants.each do |model|
          next if model.abstract_class?
          model.reset_column_information
        end
      end
      
      Rails.logger.info "[SchemaCacheRecovery] Schema cache recovery completed successfully"
      
      # Send notification to monitoring system
      notify_recovery_performed(error, model_name)
      
    rescue => recovery_error
      Rails.logger.error "[SchemaCacheRecovery] Recovery failed: #{recovery_error.message}"
      raise error # Re-raise original error if recovery fails
    end
  end

  def extract_model_name_from_error(error)
    # Try to extract model name from error message
    # Example: "undefined method `maker_nm' for #<TAggregateRankingPoint"
    if match = error.message.match(/#<(\w+)/)
      return match[1]
    end
    
    # Try to extract from backtrace
    error.backtrace.each do |line|
      if match = line.match(/app\/models\/(\w+)\.rb/)
        return match[1].camelize
      end
    end
    
    nil
  end

  def notify_recovery_performed(error, model_name)
    # Send to error reporting service (e.g., Sentry, Bugsnag)
    if defined?(Sentry)
      Sentry.capture_message(
        "Schema cache recovery performed",
        level: :warning,
        extra: {
          original_error: error.message,
          model_name: model_name,
          recovery_count: @recovery_count,
          backtrace: error.backtrace.first(5)
        }
      )
    end
    
    # Log for monitoring
    Rails.logger.warn "[SchemaCacheRecovery] Recovery notification sent for #{model_name || 'global'} recovery"
  end
end
