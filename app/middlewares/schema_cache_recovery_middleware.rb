class SchemaCacheRecoveryMiddleware
  def initialize(app)
    @app = app
    @recovery_count = 0
    @last_recovery_time = nil
  end

  # Test method to simulate schema cache corruption (only for development/staging)
  def self.simulate_corruption(model_class = TAggregateRankingPoint)
    return unless Rails.env.development? || Rails.env.staging?

    Rails.logger.info "[TEST] Simulating schema cache corruption for #{model_class.name}"

    # More aggressive corruption simulation
    begin
      # 1. Clear all cached column information
      model_class.instance_variable_set(:@columns_hash, nil)
      model_class.instance_variable_set(:@columns, nil)
      model_class.instance_variable_set(:@attribute_names, nil)
      model_class.instance_variable_set(:@content_columns, nil)

      # 2. Remove attribute methods
      model_class.undefine_attribute_methods

      # 3. Override method_missing to simulate corruption
      model_class.define_singleton_method(:method_missing) do |method_name, *args, &block|
        if method_name.to_s.end_with?('_nm') || method_name.to_s.end_with?('_id')
          raise NoMethodError, "undefined method `#{method_name}' for #<#{self.name}:0x#{object_id.to_s(16)}> - SIMULATED CORRUPTION"
        end
        super(method_name, *args, &block)
      end

      # 4. Override instance method_missing for instances
      corruption_module = Module.new do
        def method_missing(method_name, *args, &block)
          if method_name.to_s.end_with?('_nm') || method_name.to_s.end_with?('_id')
            raise NoMethodError, "undefined method `#{method_name}' for #<#{self.class.name} id: #{id}> - SIMULATED CORRUPTION"
          end
          super
        end
      end

      model_class.prepend(corruption_module)

      Rails.logger.info "[TEST] Schema cache corruption simulated successfully"
      true
    rescue => e
      Rails.logger.error "[TEST] Failed to simulate corruption: #{e.message}"
      false
    end
  end

  # Method to restore normal behavior
  def self.restore_normal_behavior(model_class = TAggregateRankingPoint)
    return unless Rails.env.development? || Rails.env.staging?

    Rails.logger.info "[TEST] Restoring normal behavior for #{model_class.name}"
    model_class.reset_column_information
    Rails.logger.info "[TEST] Normal behavior restored"
  end

  # Alternative: Create a real corruption scenario
  def self.create_real_corruption_scenario
    return unless Rails.env.development? || Rails.env.staging?

    Rails.logger.info "[TEST] Creating real corruption scenario..."

    # This simulates what happens in production:
    # 1. Clear the connection pool
    ActiveRecord::Base.clear_all_connections!

    # 2. Clear schema cache
    ActiveRecord::Base.clear_cache!

    # 3. Manually corrupt the columns_hash
    TAggregateRankingPoint.instance_variable_set(:@columns_hash, {})
    TAggregateRankingPoint.instance_variable_set(:@columns, [])

    # 4. Force undefine attribute methods
    TAggregateRankingPoint.undefine_attribute_methods

    Rails.logger.info "[TEST] Real corruption scenario created"
  end

  def call(env)
    @app.call(env)
  rescue NoMethodError => e
    if schema_cache_error?(e) && should_attempt_recovery?
      Rails.logger.warn "[SchemaCacheRecovery] Schema cache error detected, attempting recovery: #{e.message}"

      perform_recovery(e)

      return @app.call(env)
    end

    raise e
  end

  private

  def schema_cache_error?(error)
    error.message.include?("undefined method") &&
    error.backtrace.any? { |line| line.include?("active_record/attribute_methods") }
  end

  def should_attempt_recovery?
    now = Time.current

    if @last_recovery_time.nil? || (now - @last_recovery_time) > 1.hour
      @recovery_count = 0
    end

    @recovery_count < 1
  end

  def perform_recovery(error)
    @recovery_count += 1
    @last_recovery_time = Time.current

    Rails.logger.info "[SchemaCacheRecovery] Performing schema cache reset"

    ActiveRecord::Base.clear_cache!
    ActiveRecord::Base.descendants.each do |model|
      next if model.abstract_class?
      model.reset_column_information
    end

    Rails.logger.info "[SchemaCacheRecovery] Schema cache reset completed"
  end
end
