class SchemaCacheRecoveryMiddleware
  def initialize(app)
    @app = app
    @recovery_count = 0
    @last_recovery_time = nil
  end

  def call(env)
    @app.call(env)
  rescue NoMethodError => e
    # Check if this is a schema cache related error
    if schema_cache_error?(e) && should_attempt_recovery?
      Rails.logger.warn "[SchemaCacheRecovery] Schema cache error detected, attempting recovery: #{e.message}"

      perform_recovery(e)

      # Retry the request once after recovery
      return @app.call(env)
    end

    raise e
  end

  private

  def schema_cache_error?(error)
    error.message.include?("undefined method") &&
    error.backtrace.any? { |line| line.include?("active_record/attribute_methods") }
  end

  def should_attempt_recovery?
    now = Time.current

    # Reset counter if it's been more than an hour
    if @last_recovery_time.nil? || (now - @last_recovery_time) > 1.hour
      @recovery_count = 0
    end

    # Only attempt recovery once per hour to avoid infinite loops
    @recovery_count < 1
  end

  def perform_recovery(error)
    @recovery_count += 1
    @last_recovery_time = Time.current

    Rails.logger.info "[SchemaCacheRecovery] Performing schema cache reset"

    # Simple global recovery
    ActiveRecord::Base.clear_cache!
    ActiveRecord::Base.descendants.each do |model|
      next if model.abstract_class?
      model.reset_column_information
    end

    Rails.logger.info "[SchemaCacheRecovery] Schema cache reset completed"
  end
end
