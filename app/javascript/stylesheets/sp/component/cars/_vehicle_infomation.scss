table.vehicle-infomation {
  font-size: 14px;
  line-height: 1.3;
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  color: #666666;
  border-top: 1px solid #275079;
  font-family: $helvetica-font;

  tbody.vehicle-infomation__body {
    tr {
      th, td {
        border: 1px solid #b6b6b6;
        padding: 0.6em;
        word-break: break-all;
        text-align: left;
      }

      th.title {
        background: $gray-800;
        width: 36%;
        font-weight: normal;
        border-left: none;
        vertical-align: baseline;
      }

      td.content {
        border-right: none;
        background-color: #fff;

        .bnpl-price {
          min-height: 28.5px;
          display: block;
        }

        .table-label {
          color: $red-400;
          font-size: 20px;
          font-weight: bold;

          span.price--undefined {
            border-top: 2px solid $red-400;
            margin: 8px 0;
            width: 10px;
            display: inline-block;
          }
        }

        .price-block {
          font-size: 13px;
          p {
            color: $sm-content-color;
            word-break: break-word;
          }
          .price-text {
            min-height: 23.5px;
            display: block;
          }
        }

        .special-price-wrapper {
          background-color: $light-900;
          width: fit-content;
          margin: 0 2px;
          padding: 2px;

          .special-price-text {
            font-weight: 900;
            font-size: 12px;
          }
        }

        p.message {
          color: #e61e17;
          margin: 1em 0;
          word-break: normal;
        }

        .suggest-text {
          font-size: 12px;
          margin-bottom: 0.5em;
          color: #b0b0b0;
          word-break: normal;
          margin-top: 7px;

          .item {
            display: table-cell;
            vertical-align: middle;
            width: fit-content;
            white-space: nowrap;
          }

          .description {
            color: #414141;
            display: table-cell;
            width: 12em;
            padding-left: 0.2em;
          }
        }

        .insurance-alert {
          color: rgb(231, 37, 31);
          margin: 0px 0px 1em;
          word-break: normal;
        }

        .ac {
          background-color: $gray-800;
          border: none;

          .ac-trigger {
            font-size: 12px;
            color: #387bd8;
            text-decoration: underline;
            font: inherit;
            font-size: 12px;
            padding: inherit;

            &::after {
              display: none;
            }
          }
        }

        .change-info-area {
          background-color: $gray-800;
          border: 1px solid #b6b6b6;
          border-radius: 3px;
          padding: 16px 8px;
          padding-bottom: 25px;

          .change-info-text {
            display: block;
            margin-bottom: 1em;

            a {
              font-size: 12px;
              color: #387bd8;
            }

            span.arrow-right {
              display: inline-block;
              width: 0;
              height: 0;
              border: 4px solid transparent;
              border-left: 4px solid #387bd8;
            }

            span.arrow-right.active {
              border: 6px solid transparent;
              border-top: 4px solid #387bd8;
            }
          }
        }

        .change-country-port-area {
          display: block;

          .destination-list {
            transition: all 0.5s;
          }

          dl {
            dt.logi-fl {
              font-size: 14px;
              font-weight: bold;
              margin-bottom: 4px;
            }

            .logi-fl {
              text-align: left;
              color: #414141;
            }

            dd.logi-fl {
              select {
                width: 100%;
                margin: 0 auto 16px;
                font-size: 20px;
                background-color: #e5e5e5;
                color: $sm-content-color;
                height: 40px;
                border: revert-layer;

                &:focus {
                  border: 2px solid #e59701;
                  border-radius: 3px;
                  box-shadow: none;
                }
              }
            }
          }

          .select-country-port-warning {
            color: #e61e17;
            word-break: normal;
            padding-bottom: 10px;
          }

          .currency-text {
            font-weight: bold;
            color: #414141;
          }

          .currency-select {
            margin: 10px 0 0 0;
            width: 100%;
            background: #e5e5e5;
            color: #666666;
            font-size: 20px;
            height: 40px;
            border: revert-layer;

            &:focus {
              border: 2px solid #e59701;
              border-radius: 3px;
              box-shadow: none;
            }
          }

          ul.checkbox-area {
            li.cb {
              margin-bottom: 3px;

              span.custom-cb {
                input {
                  margin-right: 4px;
                }

                label {
                  font-size: 14px;
                  color: #000;

                  span.note {
                    color: rgb(231, 37, 31);
                  }
                }
              }
            }
          }
        }

        .button-calculate {
          .button-calculate__wrapper {
            position: relative;
            height: 55px;
            display: block;

            a {
              height: 50px;
              display: flex;
              align-items: center;
              text-decoration: none;
              border-radius: 5px;
              background-color: $light-green;
              border: none;
              box-shadow: 0px 4px 0px 0px $dark-green;
            }

            span.icon-calculator {
              margin-left: 23px;
              font-size: 24px;
              color: #fff;
              text-shadow: 0 -1px 0.5px $gray-100;
            }

            button.calculate-btn {
              color: #fff;
              font-weight: bold;
              font-size: 20px;
              background: none;
              border: none;
              margin: auto;
              font-family: $helvetica-font;
            }
          }
        }

        .ask-best-price-button {
          background: $orange;
          box-shadow: 0 4px 0 0 $dark-orange-100;
          border: none;
          border-radius: 5px;
          text-shadow: 0 -.5px .5px $gray-100;
          color: #fff;
          padding: 10px 12px;
          font-size: 14px;
          text-decoration: none;
          vertical-align: middle;
        }

        span.discount-percent {
          border: 1px solid #ff001d;
          color: #ff001d;
          text-align: center;
          font: normal normal bold 12px/14px Roboto;
          padding: 2px 4px;
          position: relative;
          top: -1px;
        }

        .have-discount-percent {
          display: flex;
          align-items: center;
          flex-wrap: wrap;
        }
      }
    }
  }
}

.special-price-popup {
  text-align: center;
  font-family: $helvetica-font;

  .background-area {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #424242;
    position: fixed;
    opacity: .7;
  }

  .special-price-content {
    background: #fff;
    top: 100px;
    left: 5%;
    position: fixed;
    width: 90%;
    padding: 20px;
    border-radius: 10px;
    z-index: 1000;

    .title {
      font-size: 18px;
      font-weight: 700;
    }

    button.close-popup {
      padding: 2px 8px 2px 8px;
      font-size: 16px;
      background: linear-gradient(#f9f9f9, #d9d9d9);
      border-radius: 7px;
      color: #807b6f;
      border: 1px solid $gray-500;
    }
  }
}

.title-detail {
  font-size: 14px;
  font-family: $helvetica-font;
}

.vehicle__detail-price-area {
  background-color: $light-900;
}

.finance-alert {
  font-size: 14px;
  color: #f00;
  margin: 10px;
}
