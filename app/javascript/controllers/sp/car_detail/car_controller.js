import { Controller } from 'stimulus'
import { ajax } from '@rails/ujs'
import { estPriceAlerts, loadOptions, execEstTotalPrice, displayTotalPrice,
         changeTradetpots, pickPriceFormData, checkCountryPreshipRequired,
         displayBNPLPrice, checkCountryShippingDefault } from '../../shared/est_price_module'
import { handleSubmitContactSeller } from '../../shared/validate_registration_year_module'
import Accordion from 'accordion-js'
import 'accordion-js/dist/accordion.min.css'
import { changeFavorite, loadFavorite } from '../../../components/favotite_action';
import { setOfferErrorMessage } from '../../shared/thankyou_error_handle'
import { isUserLogin } from '../../shared/users_api'
import { offerCarDetailParams } from '../../shared/offer_params'
import preloadImages from '../../sp/preload_images'
import { getDataURLSearchParams } from '../../shared/url_search_param'
import { getSellerInfo } from '../../shared/seller_info'
import { fetchInquiriesCounter } from '../../shared/inquiry_car'
import passAjaxInvalidAuthenticityToken from '../../shared/authenticity_hanlder'
import Cookies from 'js-cookie'
import { EMAIL_REGEX, CONFIRM_MSG, TARGET_COUNTRY_CODE } from '../../shared/constants'
import LazyLoad from 'vanilla-lazyload'
import { getInquiryCars } from '../../shared/inquiry_car'
import loadModalAssist from '../../shared/modal_assist'
import sendRegisterCouponApi from '../../shared/modal_coupon'
import redirectToPath from '../../shared/redirector'
import { activeTab, enableFinanceTab } from '../../shared/tab'

const COMMENTMAPPING = {
  cb_ask_quote: {
    singleComment: 'I would like to get a quote',
    combinedComment: ''
  },
  cb_ask_stock: {
    singleComment: 'I would like to confirm the stock availability',
    combinedComment: 'the stock availability'
  },
  cb_ask_shipping: {
    singleComment: 'I would like to know the shipping schedule',
    combinedComment: 'the shipping schedule'
  }
}


export default class extends Controller {
  static targets = [
    'countrySelectOptions',
    'portSelectOptions',
    'insuranceAlert',
    'displayTotalPriceEl',
    'elNotEstimated',
    'btnCalculate',
    'symbolAsterisk',
    'checkboxShipping',
    'checkboxInsurance',
    'checkboxInspection',
    'estPriceForm',
    'incoterm',
    'nearestPort',
    'priceEsimatedBlock',
    'priceNotEsimatedBlock',
    'specialPricePopup',
    'serverKind',
    'serverYear',
    'serverBeforeYear',
    'askPriceBottom',
    'estPriceForm',
    'catalogArea',
    'selectCurrency',
    'sellerInfoArea',
    'inquiriesCounter',
    'detailContactModal',
    'cbDontShowMsgOffer',
    'recommendCars',
    'askMoreBox',
    'payInstallmentsBox',
    'displayBNPLPriceEl',
    'tabEl'
  ]

  static values = {
    itemIds: Array,
    isClickFavorite: Boolean
  }

  initialize() {
    this.isClickFavoriteValue = false
  }

  connect() {
    preloadImages()
    this.showDetailContactModal()
    this.destination = new Accordion('.accordion-wrapper', {
      duration: 300,
      beforeOpen: (currentElement) => currentElement.querySelector('.arrow-right').classList.add('active'),
      beforeClose: (currentElement) => currentElement.querySelector('.arrow-right').classList.remove('active')
    })
    window.addEventListener('load', () => {
      setTimeout(() => {
        this.implementEstimateTotalPrice(true)
        this.loadPortInfo(this.countrySelectOptionsTarget, true)
        this.showNearestPort()
        loadFavorite(this.itemIdsValue)
      }, 100)
    })
    this.handleConfirmInquire()
    this.scrollListener = this.showAskPriceBottomWhenScroll.bind(this)
    window.addEventListener('scroll', this.scrollListener)
    this.loadCatalog()
    this.loadRecommendCars()
    getSellerInfo(this)
    if (this.hasInquiriesCounterTarget) fetchInquiriesCounter(this.inquiriesCounterTarget)
    loadModalAssist()
    this.defaultTab()
  }

  disconnect() {
    window.removeEventListener('scroll', this.scrollListener)
  }

  showDetailContactModal() {
    if (Cookies.get('dontShowMsgOffer') === undefined && this.hasDetailContactModalTarget) {
      setTimeout(() => {
        const financeTab = document.querySelector('#finance_tab')
        if (financeTab.disabled || financeTab.hidden) {
          let btnInquiryNext = document.querySelector('[data-car-target="askMoreBox"] .detail__contact_btn')
          let payInstallmentsBox = document.querySelector('[data-car-target="payInstallmentsBox"]')
          let btnInquiryNow = payInstallmentsBox.querySelector('.detail__contact_btn')
          btnInquiryNext.innerHTML = btnInquiryNow.innerHTML
          payInstallmentsBox.remove()
        }
        this.detailContactModalTarget.classList.add('appearance')
      }, 60000);
    }
  }

  showPayInstallmentsModal() {
    let freeText = ''
    const checkedLists = this.askMoreBoxTarget.querySelectorAll('.detail__contact_slick_checkbox_lists input[type="checkbox"]:checked')

    const nameLists = Array.from(checkedLists).map(checkbox => checkbox.name)
    const indexOfCbAskOther = nameLists.indexOf('cb_ask_others')
    if (indexOfCbAskOther !== -1) {
      freeText = this.askMoreBoxTarget.querySelector('textarea').value
      if (freeText === '') return this.detailContactNotifyRequireInput()
    }
    this.askMoreBoxTarget.classList.add('d-none')
    this.payInstallmentsBoxTarget.classList.remove('d-none')
  }

  handleDontShowPopupAskmore() {
    // when inquiry with popup
    if (this.cbDontShowMsgOfferTarget.checked)
      Cookies.set('dontShowMsgOffer', 'true', { expires: 90, path: '/' })
  }

  closeDetailContactModalAndShowCoupon(e) {
    // when click outside or close button on askmore popup
    this.detailContactModalTarget.classList.remove('appearance')
    e.stopPropagation();
    if(!Cookies.get('coupon_was_used')){
      sendRegisterCouponApi()
    }
    if (this.cbDontShowMsgOfferTarget.checked)
      Cookies.set('dontShowMsgOffer', 'true', { expires: 90, path: '/' })
  }

  handleCbDontShowMsgOffer(e) {
    this.cbDontShowMsgOfferTargets.map(checkbox => checkbox.checked = e.target.checked)
  }

  similarChecking(e) {
    const target = e.currentTarget
    const name   = target.name
    const status = target.checked
    const similarCheckbox = document.querySelectorAll(`input[name="${name}"]`)
    similarCheckbox.forEach(checkbox => checkbox.checked = status)
  }

  changeIconFavorite(event) {
    changeFavorite(event, this)
  }

  showAskPriceBottomWhenScroll() {
    let scrollTop = window.pageYOffset
    const askPriceBottom = this.askPriceBottomTarget
    const pointToShowPos = this.estPriceFormTarget.offsetTop

    if (scrollTop >= pointToShowPos) {
      askPriceBottom.classList.add('show')
    } else {
      askPriceBottom.classList.remove('show')
    }
  }

  changeTab(e) {
    e.preventDefault()
    const target = e.currentTarget
    const currentTab = $(target).data('tab')

    $(target).parent().find('.tabs').addClass('deactive')
    $(target).removeClass('deactive')
    $('.tabs-content').hide()
    $('.wrapp__tabs-content').find("[data-tab='" + currentTab + "']").show()
  }

  async loadCatalog() {
    const catalogArea = this.catalogAreaTarget
    const url = this.catalogAreaTarget.dataset.carCatalogUrl

    await $.ajax({
      url: url,
      type: 'GET',
      success: function(response) {
        catalogArea.outerHTML = response
      }
    })
  }

  execInquiryWithMsg(e) {
    let freeText = ''
    const checkedLists = this.askMoreBoxTarget.querySelectorAll('.detail__contact_slick_checkbox_lists input[type="checkbox"]:checked')

    if (checkedLists.length === 0) {
      this.handleDontShowPopupAskmore()
      return this.execInquiry(e, freeText)
    }

    const nameLists = Array.from(checkedLists).map(checkbox => checkbox.name)
    const indexOfCbAskOther = nameLists.indexOf('cb_ask_others')
    if (indexOfCbAskOther !== -1) {
      freeText = this.askMoreBoxTarget.querySelector('textarea').value
      if (freeText === '') return this.detailContactNotifyRequireInput()

      nameLists.splice(indexOfCbAskOther, 1);
    }

    if (freeText.match(EMAIL_REGEX) && !confirm(CONFIRM_MSG)) return

    if (nameLists.length === 0) {
      this.handleDontShowPopupAskmore()
      localStorage.setItem('offerOtherText', freeText)
      return this.execInquiry(e, freeText)
    }

    const firstText  = COMMENTMAPPING[nameLists.shift()].singleComment
    const secondText = nameLists.map(name => COMMENTMAPPING[name].combinedComment).join(' and ')
    const finalText  = this.commentTemplate(firstText, secondText, freeText)
    localStorage.setItem('offerOtherText', finalText)
    this.execInquiry(e, finalText)
    this.handleDontShowPopupAskmore()
  }

  detailContactNotifyRequireInput() {
    const textareaList = document.querySelectorAll('.detail__contact_freetext textarea')
    const textNoticeList = document.querySelectorAll('.detail__contact_freetext .detail__contact_notice')
    textareaList.forEach(textarea => {
      textarea.classList.add('notice')
      setTimeout(() => textarea.classList.remove('notice'), 1500)
    })
    textNoticeList.forEach(textEl => textEl.style.display = 'block')
  }

  commentTemplate(firstText, secondText, freeText) {
    return secondText === '' ? `${firstText}.\n${freeText}` : `${firstText}, ${secondText}.\n${freeText}`
  }

  applySimilarText(e) {
    const target = e.currentTarget
    const name   = target.name
    const currentValue  = target.value
    const textareaLists = document.querySelectorAll(`textarea[name="${name}"]`)
    const textNoticeList = document.querySelectorAll('.detail__contact_freetext .detail__contact_notice')
    textareaLists.forEach(textarea => textarea.value = currentValue)
    textNoticeList.forEach(textEl => textEl.style.display = 'none')
  }

  execInquiry(e, msg = '') {
    e.preventDefault()
    if (handleSubmitContactSeller(e)) this.handleSendOffer(msg)
  }

  handleConfirmInquire() {
    const self = this

    $("#modal-check-year .inquire-btn").on("click", function() {
      $("#modal-check-year").modal("hide")
      self.handleSendOffer(localStorage?.offerOtherText)
      localStorage.removeItem('offerOtherText')
    })
  }

  handleSendOffer(msg = '', id = null) {
    const carId = id || $(".car-detail-wrapper").data('car-item-ids-value')[0]
    const sellerId = $('.car-detail-wrapper').data('car-seller-id')
    const isBnpl = (document.querySelector('.detail__contact_modal.appearance')) ? $('#rad_pay_installments').is(':checked') : $('#finance_content').is(':visible')

    const offerData = {
      item_id: carId,
      is_bnpl: isBnpl,
      seller_id: sellerId,
      exrate: this.selectCurrencyTarget.value,
      message: msg,
      referrer: sessionStorage.getItem("referrer"),
      ...offerCarDetailParams(this.estPriceFormData())
    }

    isUserLogin().then((isUserSignedIn) => {
      if (isUserSignedIn) {
        $('#overlay_loading').show()
        $.ajax({
          url: "/ajax_v2/transactions/offers",
          type: "POST",
          data: offerData,
          success: function(data) {
            $('#overlay_loading').hide()
            setOfferErrorMessage(data)
            window.location.href = data.url
          },
          error: function(response) { passAjaxInvalidAuthenticityToken(response) }
        })
      } else {
        $('#overlay_loading').show()
        const { country_number, port_id } = offerData
        const stringData = {
          item_id: carId,
          country_number,
          port_id,
          comment: msg
        }

        $.ajax({
          url: "/ajax_v2/transactions/offers/temp",
          type: "POST",
          data: stringData,
          success: function(data) {
            if (data.success == true) {
              $('#overlay_loading').hide()
              let url = '/simple/loginregist?id=' + offerData.item_id + '&ti=' + data.response.temporaryContactId + '&hs=' + data.response.hs + '&icn=' + offerData.country_number + '&ipt=' + offerData.port_id + '&iir='
                          + offerData.insurance + '&iip=' + offerData.inspection + '&ifr=' + offerData.shipping + '&ier=' + offerData.exrate + '&is_bnpl=' + offerData.is_bnpl + '&seller_id=' + offerData.seller_id
              window.location.href = url
            } else {
              window.location.reload()
            }
          },
          error: function(response) { passAjaxInvalidAuthenticityToken(response) }
        })
      }
    })
  }

  toggleCheckbox(e) {
    let selectedValue = e.currentTarget.value
    const cbShp  = document.querySelector('input#dt_checkbox_shipping')
    const cbInsu = document.querySelector('input#dt_checkbox_insurance')
    const cbInsp = document.querySelector('input#dt_checkbox_inspection')
    let checkbox = document.querySelectorAll('.option-checkbox')
    $('#ett_select_port').val(selectedValue)

    if (selectedValue == 0) {
      cbShp.disabled  = true
      cbInsu.disabled = true
      cbInsp.disabled = true
      this.btnCalculateTarget.disabled = true
      this.elNotEstimatedTarget.classList.remove('d-none')
      checkbox.forEach((cb) => cb.style.opacity = '.3')
    } else {
      cbShp.disabled  = false
      cbInsu.disabled = false
      cbInsp.disabled = checkCountryPreshipRequired(this.countrySelectOptionsTarget.value)
      this.btnCalculateTarget.disabled = false
      this.elNotEstimatedTarget.classList.add('d-none')
      checkbox.forEach((cb) => cb.style.opacity = '1')
    }

    let data = this.estPriceFormData()
    if ( data['port_id'] === '0' ) return

    data['just_update_itemdetailshipestimate'] = true

    ajax({
      url: '/ajax_v2/estimated_total_prices/cookies',
      type: 'put',
      dataType: 'jsonp',
      data: getDataURLSearchParams(data),
      error: function(response) { passAjaxInvalidAuthenticityToken(response) }
    })
  }

  loadPortInfo(e, init = false) {
    const self = this
    let data
    const ettSelectCountry = $('#ett_select_country').val()
    const currentTarget = e.target === undefined ? e : e.target
    if (init && ettSelectCountry != 'undefined') {
      data = { country_number: ettSelectCountry }
      this.countrySelectOptionsTarget.value = ettSelectCountry
    } else {
      data = { country_number: currentTarget.value }
      $('#ett_select_country').val(currentTarget.value)
    }
    const selectedValue = $('#ett_select_port').val() != 'undefined' ? $('#ett_select_port').val() : this.portSelectOptionsTarget.dataset.portSelected
    let checkbox = document.querySelectorAll('.option-checkbox')

    ajax({
      url: '/ajax_v2/estimated_total_prices/nearest_ports',
      type: 'get',
      dataType: 'jsonp',
      data: getDataURLSearchParams(data),
      success: function (response) {
        self.portSelectOptionsTarget.innerHTML = ''
        self.portSelectOptionsTarget.add(new Option('Please Select', 0))
        self.btnCalculateTarget.disabled = true
        checkbox.forEach((cb) => cb.style.opacity = '.3' )

        if (response.ports === undefined || response.ports.length == 0) {
          self.portSelectOptionsTarget.disabled = true
        } else {
          self.portSelectOptionsTarget.disabled = false
          self.elNotEstimatedTarget.classList.remove('d-none')
          response.ports.forEach(port =>{
            let option = new Option(port[1], port[0])
            self.portSelectOptionsTarget.add(option)
          })

          const selectedValueIsCollect = response.ports.find((portChild) => parseInt(selectedValue) == portChild[0])
          const checkboxShipping = document.querySelector('input#dt_checkbox_shipping')
          const checkboxInsurance = document.querySelector('input#dt_checkbox_insurance')
          const checkboxInspection = document.querySelector('input#dt_checkbox_inspection')

          if (selectedValueIsCollect && init) {
            let optionSelected = self.portSelectOptionsTarget.querySelector(`option[value="${selectedValue}"]`)

            optionSelected.selected = true
            self.btnCalculateTarget.disabled = false
            checkbox.forEach((cb) => cb.style.opacity = '1' )
            self.elNotEstimatedTarget.classList.add('d-none')

            checkboxShipping.disabled = false
            checkboxInsurance.disabled = false

            checkboxShipping.checked = checkCountryShippingDefault(self.countrySelectOptionsTarget.value)

            if (checkCountryPreshipRequired(self.countrySelectOptionsTarget.value)) {
              checkboxInspection.disabled = true
              checkboxInspection.checked = true
              checkboxInspection.parentElement.style.opacity = '.3'
            } else {
              checkboxInspection.disabled = false
            }
          } else {
            checkboxShipping.disabled = true
            checkboxInsurance.disabled = true
            checkboxInspection.disabled = true
            $('#ett_select_port').val('0')
          }
        }
      }
    })

    estPriceAlerts(this.insuranceAlertTargets, this.symbolAsteriskTarget, currentTarget.value,
      this.serverKindTarget, this.serverYearTarget, this.serverBeforeYearTarget
    )
    loadOptions(currentTarget.value, self.checkboxShippingTarget, self.checkboxInsuranceTarget, self.checkboxInspectionTarget)
    if (init) {
      if ($('#ett_tradetpots').val() == 'undefined') return

      const { shipping, insurance, inspection } = JSON.parse($('#ett_tradetpots').val())
      document.querySelector('input#dt_checkbox_shipping').checked = shipping
      document.querySelector('input#dt_checkbox_insurance').checked = insurance
      document.querySelector('input#dt_checkbox_inspection').checked = inspection
    } else {
      document.querySelector('input#dt_checkbox_shipping').checked = checkCountryShippingDefault(self.countrySelectOptionsTarget.value)
      document.querySelector('input#dt_checkbox_insurance').checked = false
      document.querySelector('input#dt_checkbox_inspection').checked = true
      this.handleChangeTradetpots()
    }
  }

  handleChangeTradetpots() {
    changeTradetpots('ett_tradetpots', 'dt_checkbox_shipping', 'dt_checkbox_insurance', 'dt_checkbox_inspection')
  }

  changeCurrency(e) {
    let data = { currency: e.target.value }

    ajax({
      url: '/ajax_v2/currencies',
      type: 'POST',
      dataType: 'jsonp',
      data: getDataURLSearchParams(data),
      success: function () {
        window.location.reload()
      },
      error: function(response) { passAjaxInvalidAuthenticityToken(response) }
    })
  }

  calculateEstTotalPrice(event) {
    event.preventDefault()
    if (this.btnCalculateTarget.disabled === true) return

    this.priceEsimatedBlockTarget.classList.remove('d-none')
    this.priceNotEsimatedBlockTarget.classList.add('d-none')

    this.saveCookieEstPrice()
    this.destination.close(0)
  }

  saveCookieEstPrice() {
    const self = this
    let form = this.estPriceFormTarget
    let data = this.estPriceFormData()
    ajax({
      url: '/ajax_v2/estimated_total_prices/cookies',
      type: 'put',
      dataType: 'jsonp',
      data: getDataURLSearchParams(data),
      success: (response) => {
        let destination = form.querySelector('select#select_your_port').selectedOptions[0].innerText
        const incotermPort = { icoterm: response.incoterms, port: destination }

        self.incotermTargets.map(target => target.innerText = incotermPort['icoterm'])
        self.nearestPortTargets.map(target => target.innerText = incotermPort['port'])
        $('#ett_incoterm_port').val(JSON.stringify(incotermPort))
        this.implementEstimateTotalPrice()
      },
      error: function(response) { passAjaxInvalidAuthenticityToken(response) }
    })
  }

  estPriceFormData() {
    const form = this.estPriceFormTarget
    return {
      country_number: form.querySelector('select#select_your_country').value,
      port_id: form.querySelector('select#select_your_port').value || form.querySelector('select#select_your_port').dataset.portSelected,
      shipping: !this.checkboxShippingTarget.classList.contains('d-none') && !form.querySelector('input#dt_checkbox_shipping').disabled
                && form.querySelector('input#dt_checkbox_shipping').checked ? 1 : 0,
      insurance: !this.checkboxInsuranceTarget.classList.contains('d-none') && !form.querySelector('input#dt_checkbox_insurance').disabled
                 && form.querySelector('input#dt_checkbox_insurance').checked ? 1 : 0,
      inspection: checkCountryPreshipRequired(this.countrySelectOptionsTarget.value) || !this.checkboxInspectionTarget.classList.contains('d-none')
                  && !form.querySelector('input#dt_checkbox_inspection').disabled && form.querySelector('input#dt_checkbox_inspection').checked ? 1 : 0,
      show_shipping: $('input#dt_checkbox_shipping').closest('li').is(':visible') ? 1 : 0
    }
  }

  implementEstimateTotalPrice(init = false) {
    const priceFormData = pickPriceFormData(init, 'ett_country_port', this.estPriceFormData())

    execEstTotalPrice(priceFormData, this.itemIdsValue, false, (response) => {
      if (Object.keys(response.total_price_items).length == 0) return;

      this.displayTotalPriceElTarget.innerText = displayTotalPrice(response.total_price_items[0])
      this.displayBNPLPriceElTarget.innerText = displayBNPLPrice(response.total_price_items[0])
    })
  }

  showNearestPort() {
    if ($('#ett_incoterm_port').val() == 'undefined') return

    const { icoterm, port } = JSON.parse($('#ett_incoterm_port').val())
    this.incotermTarget.innerText = icoterm
    this.nearestPortTarget.innerText = port

    this.priceEsimatedBlockTarget.classList.remove('d-none')
    this.priceEsimatedBlockTarget.classList.add('d-block')
    this.priceNotEsimatedBlockTarget.classList.remove('d-block')
    this.priceNotEsimatedBlockTarget.classList.add('d-none')
  }

  showSpecialPricePopup() {
    this.specialPricePopupTarget.classList.remove('d-none')
  }

  closeSpecialPricePopup(e) {
    e.preventDefault()
    this.specialPricePopupTarget.classList.add('d-none')
  }

  async loadRecommendCars() {
    const recommendCars = this.recommendCarsTarget
    const url = recommendCars.dataset.url

    await $.ajax({
      url: url,
      type: 'GET',
      success: function (response) {
        recommendCars.outerHTML = response
        const lazyContent = new LazyLoad({ use_native: true });
        const carIds = document.querySelector('.recommend-cars-wrapper').dataset.carIds
        getInquiryCars(JSON.parse(carIds))
      }
    })
  }

  execRecommendOffer(event, msg = '') {
    event.preventDefault()
    if (handleSubmitContactSeller(event)) this.handleSendOffer(msg, parseInt(event.target.dataset.carId))
  }

  redirectToDetailPath(e) {
    redirectToPath(e)
  }

  showTab(e) {
    e.preventDefault()
    activeTab(e.target)
  }

  defaultTab() {
    const cashTab = document.querySelector('#cash_tab')
    if (cashTab.dataset.tabDefault === 'true') {
      $('.vehicle__detail-price-area').addClass('p-5')
      activeTab(cashTab)
    } else {
      this.tabElTargets.map(el => el.hidden = true)
      $('.tab-content__block[data-tab-content="cash"]').addClass('d-block')
    }
  }

  enableFinanceTab() {
    // Prevent the calculate button click event when country and port are not selected
    if (this.btnCalculateTarget.disabled) return

    // the user has never selected Kenya/Zambia/Uganda before or now
    if (this.tabElTarget.hidden && !TARGET_COUNTRY_CODE.includes(parseInt(this.countrySelectOptionsTarget.value))) return

    // the user has never selected Kenya/Zambia/Uganda before but now selected
    if (this.tabElTarget.hidden) {
      $('.vehicle__detail-price-area').addClass('p-5')
      this.tabElTargets.map(el => el.hidden = false)
      $('.tab-content__block[data-tab-content="cash"]').removeClass('d-block')
      activeTab(this.tabElTarget)
    }
    enableFinanceTab()
  }
}
